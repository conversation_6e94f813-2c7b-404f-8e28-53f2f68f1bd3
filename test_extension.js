// Test script to verify extension functionality
console.log('🧪 Testing refactored extension...');

// Test 1: Check if popup.js loads without errors
try {
    console.log('✅ Test 1: popup.js loaded successfully');
} catch (error) {
    console.error('❌ Test 1 failed:', error);
}

// Test 2: Check if all managers are available
const testManagers = async () => {
    try {
        // These should be available globally after popup.js loads
        if (typeof PopupController !== 'undefined') {
            console.log('✅ Test 2a: PopupController available');
        } else {
            console.log('❌ Test 2a: PopupController not available');
        }
        
        console.log('✅ Test 2: Manager availability check completed');
    } catch (error) {
        console.error('❌ Test 2 failed:', error);
    }
};

// Test 3: Check if extension manifest is valid
const testManifest = () => {
    try {
        if (chrome && chrome.runtime && chrome.runtime.getManifest) {
            const manifest = chrome.runtime.getManifest();
            console.log('✅ Test 3: Manifest loaded:', manifest.name, 'v' + manifest.version);
        } else {
            console.log('ℹ️ Test 3: Not in extension context');
        }
    } catch (error) {
        console.error('❌ Test 3 failed:', error);
    }
};

// Run tests
testManagers();
testManifest();

console.log('🎉 Extension testing completed!');
