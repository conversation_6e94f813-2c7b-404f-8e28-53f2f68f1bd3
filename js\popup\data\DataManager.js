/**
 * Data Manager
 * Handles all storage operations, history management, and data persistence
 */
import { BaseManager } from '../core/BaseManager.js';
import { PAGINATION_CONFIG } from '../../../config.js';

export class DataManager extends BaseManager {
    constructor(controller) {
        super(controller);
    }

    async init() {
        await super.init();
        // Check for pending analysis and context menu actions on initialization
        await this.checkPendingAnalysis();
        await this.checkContextMenuActions();
    }

    /**
     * Load API key from storage
     */
    async loadApiKey() {
        try {
            const result = await chrome.storage.sync.get(['agentHustleApiKey']);
            this.controller.apiKey = result.agentHustleApiKey;
        } catch (error) {
            console.error('Error loading API key:', error);
        }
    }

    /**
     * Save API key to storage
     */
    async saveApiKey(apiKey) {
        try {
            await chrome.storage.sync.set({ agentHustleApiKey: apiKey });
            this.controller.apiKey = apiKey;
            this.controller.uiManager.updateApiStatus();
            this.controller.uiManager.updateUI();
        } catch (error) {
            console.error('Error saving API key:', error);
            this.controller.uiManager.showError('Failed to save API key');
        }
    }

    /**
     * Save analysis to history
     */
    async saveAnalysis(analysisType, result) {
        try {
            const newEntry = {
                id: `analysis-${Date.now()}`,
                date: new Date().toISOString(),
                analysisType,
                result
            };

            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            history.push(newEntry);

            await chrome.storage.local.set({ hustleplugAnalysis: history });
        } catch (error) {
            console.error('Error saving analysis:', error);
        }
    }

    /**
     * Load and display analysis history with pagination
     */
    async loadAndDisplayAnalysis() {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const allHistory = data.hustleplugAnalysis || [];
            const historyList = document.getElementById('analysisHistoryList');
            const paginationContainer = document.getElementById('historyPagination');

            if (allHistory.length === 0) {
                historyList.innerHTML = '<p style="text-align: center; color: #888;">No analysis history found.</p>';
                if (paginationContainer) {
                    paginationContainer.innerHTML = '';
                }
                return;
            }

            // Sort by date (newest first)
            const sortedHistory = allHistory.sort((a, b) => new Date(b.date) - new Date(a.date));
            
            // Get paginated data
            const paginatedData = this.controller.historyPagination.getPaginatedData(sortedHistory);
            const paginatedHistory = paginatedData.data;
            const pagination = paginatedData.pagination;
            
            // Display paginated items
            paginatedHistory.forEach(item => {
                const itemEl = document.createElement('div');
                itemEl.className = 'history-item';
                itemEl.dataset.analysisId = item.id; // Add ID to dataset

                // Handle both string and object based results for backward compatibility
                const resultContent = typeof item.result === 'object' && item.result !== null && item.result.content ? item.result.content : item.result;
                const preview = this.controller.analysisManager.formatAnalysisContent(resultContent, true);
                
                itemEl.innerHTML = `
                    <div class="history-header">
                        <h5>${item.analysisType}</h5>
                        <div class="history-actions">
                            <button class="btn-icon telegram-send-btn" data-analysis-id="${item.id}" title="Send to Telegram">📱</button>
                            <button class="btn-icon discord-send-btn" data-analysis-id="${item.id}" title="Send to Discord">💬</button>
                            <button class="btn-icon delete-btn" data-analysis-id="${item.id}" title="Delete">🗑️</button>
                        </div>
                    </div>
                    <div class="history-content">
                        <p>${preview}</p>
                    </div>
                    <div class="history-footer">
                        <span class="history-date">${new Date(item.date).toLocaleString()}</span>
                        <a href="#" class="view-details-link" data-analysis-id="${item.id}">View Details</a>
                    </div>
                `;
                
                historyList.appendChild(itemEl);
            });

            // Update pagination controls
            if (paginationContainer) {
                paginationContainer.innerHTML = this.controller.historyPagination.generatePaginationHTML(pagination);
            }
            
        } catch (error) {
            console.error('Error loading analysis history:', error);
            const historyList = document.getElementById('analysisHistoryList');
            if (historyList) {
                historyList.innerHTML = '<p style="text-align: center; color: #f44;">Error loading history.</p>';
            }
        }
    }

    /**
     * View a specific analysis from history
     */
    async viewAnalysisFromHistory(analysisId) {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            const analysisItem = history.find(item => item.id === analysisId);

            if (analysisItem) {
                this.controller.analysisManager.displayResults(
                    analysisItem.result, 
                    analysisItem.analysisType, 
                    new Date(analysisItem.date)
                );
                this.controller.uiManager.showSection('resultsSection');
            } else {
                this.controller.uiManager.showError('Could not find the selected analysis.');
            }
        } catch (error) {
            console.error('Error viewing analysis from history:', error);
            this.controller.uiManager.showError('Failed to load the analysis.');
        }
    }

    /**
     * Delete analysis from history
     */
    async deleteAnalysis(analysisId) {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            
            // Filter out the analysis to delete
            const updatedHistory = history.filter(item => item.id !== analysisId);
            
            // Update storage
            await chrome.storage.local.set({ hustleplugAnalysis: updatedHistory });
            
            // Check if current page is now empty and adjust if needed
            const currentPageData = this.controller.historyPagination.getPaginatedData(updatedHistory);
            if (currentPageData.data.length === 0 && this.controller.historyPagination.currentPage > 1) {
                this.controller.historyPagination.currentPage = Math.max(1, this.controller.historyPagination.currentPage - 1);
            }
            
            // Refresh the display
            await this.loadAndDisplayAnalysis();
            
            this.controller.uiManager.showSuccess('Analysis deleted successfully');
        } catch (error) {
            console.error('Error deleting analysis:', error);
            this.controller.uiManager.showError('Failed to delete analysis');
        }
    }

    /**
     * Check for pending analysis from content script
     */
    async checkPendingAnalysis() {
        try {
            const result = await chrome.storage.local.get(['pendingAnalysis']);
            if (result.pendingAnalysis) {
                const { type, data, timestamp } = result.pendingAnalysis;
                
                // Only process if it's recent (within 10 seconds)
                if (Date.now() - timestamp < 10000) {
                    console.log('Found pending analysis:', type, data.substring(0, 50) + '...');
                    
                    // Clear the pending analysis
                    await chrome.storage.local.remove(['pendingAnalysis']);
                    
                    // Trigger the appropriate analysis
                    if (type === 'selection' && data) {
                        await this.controller.analysisManager.analyzeSelectionWithText(data);
                    }
                } else {
                    // Clear old pending analysis
                    await chrome.storage.local.remove(['pendingAnalysis']);
                }
            }
        } catch (error) {
            console.error('Error checking pending analysis:', error);
        }
    }

    /**
     * Check for context menu actions
     */
    async checkContextMenuActions() {
        try {
            const result = await chrome.storage.local.get([
                'showUpgradeSection', 
                'showCustomAnalysis', 
                'showHistorySection',
                'contextMenuData',
                'upgradeReason',
                'contextMenuTrigger'
            ]);
            
            // Handle upgrade section request
            if (result.showUpgradeSection) {
                await chrome.storage.local.remove(['showUpgradeSection', 'upgradeReason']);
                this.controller.uiManager.showSection('upgradeSection');
                
                // Show specific message if from context menu
                if (result.upgradeReason) {
                    this.controller.uiManager.showError(result.upgradeReason + ' - Upgrade to Pro to access this feature');
                }
                return;
            }
            
            // Handle custom analysis request
            if (result.showCustomAnalysis && result.contextMenuData) {
                await chrome.storage.local.remove(['showCustomAnalysis', 'contextMenuData']);
                
                // Pre-fill custom analysis form if there's selected text
                if (result.contextMenuData.hasSelection) {
                    document.getElementById('customPrompt').value = `Analyze the following text:\n\n${result.contextMenuData.selectedText}`;
                    // Select "Selected Text" radio button
                    const selectionRadio = document.querySelector('input[name="dataSource"][value="selection"]');
                    if (selectionRadio) selectionRadio.checked = true;
                } else {
                    // Select "Full Page" radio button
                    const pageRadio = document.querySelector('input[name="dataSource"][value="page"]');
                    if (pageRadio) pageRadio.checked = true;
                }
                
                this.controller.uiManager.showSection('customForm');
                return;
            }
            
            // Handle history section request
            if (result.showHistorySection) {
                await chrome.storage.local.remove(['showHistorySection']);
                this.controller.uiManager.showSection('analysisHistorySection');
                this.loadAndDisplayAnalysis();
                return;
            }
            
            // Handle context menu triggers (immediate analysis requests)
            if (result.contextMenuTrigger) {
                await chrome.storage.local.remove(['contextMenuTrigger']);
                
                // Check if the trigger is recent (within 10 seconds)
                const triggerTime = result.contextMenuTrigger.timestamp;
                const timeDiff = Date.now() - triggerTime;
                
                if (timeDiff < 10000) { // 10 seconds
                    console.log('Processing context menu trigger:', result.contextMenuTrigger.type);
                    
                    // Show loading section immediately for user feedback
                    this.controller.uiManager.showSection('loadingSection');
                    
                    if (result.contextMenuTrigger.type === 'selection') {
                        // Trigger text analysis with loading animation
                        await this.controller.analysisManager.analyzeSelectionWithText(result.contextMenuTrigger.data);
                    } else if (result.contextMenuTrigger.type === 'page') {
                        // Trigger page analysis with loading animation
                        await this.controller.analysisManager.analyzePageWithData(result.contextMenuTrigger.data);
                    }
                    return;
                }
            }
            
        } catch (error) {
            console.error('Error checking context menu actions:', error);
        }
    }

    /**
     * Get analysis by ID
     */
    async getAnalysisById(analysisId) {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            return history.find(item => item.id === analysisId);
        } catch (error) {
            console.error('Error getting analysis by ID:', error);
            return null;
        }
    }

    /**
     * Clear all analysis history
     */
    async clearAllHistory() {
        try {
            await chrome.storage.local.set({ hustleplugAnalysis: [] });
            await this.loadAndDisplayAnalysis();
            this.controller.uiManager.showSuccess('All analysis history cleared');
        } catch (error) {
            console.error('Error clearing history:', error);
            this.controller.uiManager.showError('Failed to clear history');
        }
    }

    /**
     * Export all analysis history
     */
    async exportAllHistory() {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            
            if (history.length === 0) {
                this.controller.uiManager.showError('No history to export');
                return;
            }

            const exportData = {
                exportDate: new Date().toISOString(),
                totalAnalyses: history.length,
                analyses: history
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agent-hustle-history-${Date.now()}.json`;
            
            document.body.appendChild(a);
            a.click();
            
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.controller.uiManager.showSuccess('History exported successfully!');
        } catch (error) {
            console.error('Error exporting history:', error);
            this.controller.uiManager.showError('Failed to export history');
        }
    }
}
