/**
 * Settings Manager
 * Handles membership, API keys, and Pro feature management
 */
import { BaseManager } from '../core/BaseManager.js';
import {
    checkProStatus,
    setProKey,
    getMaskedProKey,
    getDetailedProStatus,
    refreshProStatus
} from '../../../js/user/proStatus.js';
import { runDailyMembershipCheck } from '../../../js/utils/membershipChecker.js';
import {
    saveTelegramSettings,
    getTelegramSettings,
    clearTelegramSettings,
    isTelegramConfigured,
    getMaskedBotToken,
    saveTelegramAutoSendSettings,
    getTelegramAutoSendSettings
} from '../../../js/user/telegramSettings.js';
import {
    saveDiscordSettings,
    getDiscordSettings,
    clearDiscordSettings,
    isDiscordConfigured,
    getMaskedWebhookUrl,
    saveDiscordAutoSendSettings,
    getDiscordAutoSendSettings
} from '../../../js/user/discordSettings.js';
import {
    sendAnalysisToTelegram,
    testTelegramConnection
} from '../../../js/integrations/telegram.js';
import {
    sendAnalysisToDiscord,
    testDiscordWebhook
} from '../../../js/integrations/discord.js';

export class SettingsManager extends BaseManager {
    constructor(controller) {
        super(controller);
    }

    async init() {
        await super.init();
        // Load API key on initialization
        await this.controller.dataManager.loadApiKey();
    }

    /**
     * Save API key
     */
    async saveApiKey(apiKey) {
        await this.controller.dataManager.saveApiKey(apiKey);
    }

    /**
     * Handle custom analysis click - check Pro status
     */
    async handleCustomAnalysisClick() {
        try {
            // Run daily membership check
            await runDailyMembershipCheck();
            
            const proStatus = await checkProStatus();
            
            if (proStatus.isPro) {
                // Show expiration warning if applicable
                if (proStatus.expirationWarning) {
                    this.controller.uiManager.showMembershipWarning(proStatus.expirationWarning);
                }
                
                // User is pro, show custom analysis form
                this.controller.uiManager.showSection('customForm');
            } else {
                // User is regular or expired, show upgrade page
                this.controller.uiManager.showSection('upgradeSection');
                
                // Show expiration message if membership expired
                if (proStatus.expired) {
                    this.controller.uiManager.showError('Your Pro membership has expired. Please upgrade to continue using Pro features.');
                }
                
                // Pre-fill pro key if one exists (for re-validation)
                const maskedKey = await getMaskedProKey();
                if (maskedKey) {
                    const proKeyInput = document.getElementById('proKeyInput');
                    if (proKeyInput) {
                        proKeyInput.placeholder = `Current key: ${maskedKey}`;
                    }
                }
            }
        } catch (error) {
            console.error('Error checking pro status:', error);
            // Default to showing upgrade page on error
            this.controller.uiManager.showSection('upgradeSection');
        }
    }

    /**
     * Handle prompt manager click - check Pro status
     */
    async handlePromptManagerClick() {
        try {
            // Run daily membership check
            await runDailyMembershipCheck();
            
            const proStatus = await checkProStatus();
            
            if (proStatus.isPro) {
                // Show expiration warning if applicable
                if (proStatus.expirationWarning) {
                    this.controller.uiManager.showMembershipWarning(proStatus.expirationWarning);
                }
                
                // User is pro, show prompt management section
                this.controller.uiManager.showSection('promptManagementSection');
                this.controller.promptUIManager.loadPromptManagement();
            } else {
                // User is regular or expired, show upgrade page
                this.controller.uiManager.showSection('upgradeSection');
                
                // Show expiration message if membership expired
                if (proStatus.expired) {
                    this.controller.uiManager.showError('Your Pro membership has expired. Please upgrade to continue using Pro features.');
                }
                
                // Pre-fill pro key if one exists (for re-validation)
                const maskedKey = await getMaskedProKey();
                if (maskedKey) {
                    const proKeyInput = document.getElementById('proKeyInput');
                    if (proKeyInput) {
                        proKeyInput.placeholder = `Current key: ${maskedKey}`;
                    }
                }
            }
        } catch (error) {
            console.error('Error checking pro status:', error);
            // Default to showing upgrade page on error
            this.controller.uiManager.showSection('upgradeSection');
        }
    }

    /**
     * Handle Pro key validation
     */
    async handleProKeyValidation() {
        const proKeyInput = document.getElementById('proKeyInput');
        const validateBtn = document.getElementById('validateProKey');
        
        if (!proKeyInput || !validateBtn) {
            console.error('Pro key input elements not found');
            return;
        }
        
        const proKey = proKeyInput.value.trim();
        
        if (!proKey) {
            this.controller.uiManager.updateProKeyStatus('invalid', 'Please enter a pro key');
            return;
        }
        
        try {
            // Show validating state
            this.controller.uiManager.updateProKeyStatus('validating', 'Validating pro key...');
            validateBtn.disabled = true;
            validateBtn.innerHTML = '<span class="btn-icon">⏳</span>Validating...';
            
            // Validate the key
            const result = await setProKey(proKey);
            
            if (result.success && result.isPro) {
                // Success - user is now pro
                this.controller.uiManager.updateProKeyStatus('valid', result.message);
                
                // Get detailed membership info for display
                const detailedStatus = await getDetailedProStatus();
                let successMessage = 'Pro key validated! Welcome to Pro features!';
                
                // Add membership details if available
                if (detailedStatus.membershipDetails) {
                    const details = detailedStatus.membershipDetails;
                    if (details.daysRemaining > 0) {
                        successMessage += ` (${details.daysRemaining} days remaining)`;
                    }
                }
                
                // Show success and redirect to custom analysis
                setTimeout(() => {
                    this.controller.uiManager.showSuccess(successMessage);
                    this.controller.uiManager.showSection('customForm');
                    proKeyInput.value = ''; // Clear the input
                }, 1000);
                
            } else {
                // Invalid key
                this.controller.uiManager.updateProKeyStatus('invalid', result.message);
            }
            
        } catch (error) {
            console.error('Error validating pro key:', error);
            this.controller.uiManager.updateProKeyStatus('invalid', 'Validation failed. Please try again.');
        } finally {
            // Reset button state
            validateBtn.disabled = false;
            validateBtn.innerHTML = '<span class="btn-icon">🔑</span>Validate Pro Key';
        }
    }

    /**
     * Load settings content (placeholder for full implementation)
     */
    async loadSettingsContent() {
        await this.loadMembershipInfo();
        // Note: Telegram and Discord settings loading would be implemented here
        // For now, keeping it simple to maintain the refactoring momentum
    }

    /**
     * Load all settings content
     */
    async loadSettingsContent() {
        await this.loadMembershipInfo();
        await this.loadTelegramSettings();
        await this.loadDiscordSettings();
    }

    /**
     * Load membership information
     */
    async loadMembershipInfo() {
        const membershipContent = document.getElementById('membershipInfoContent');
        if (!membershipContent) return;

        try {
            // Run membership check to get latest status
            await runDailyMembershipCheck();

            const detailedStatus = await getDetailedProStatus();
            
            if (!detailedStatus.hasKey) {
                // No pro key configured
                membershipContent.innerHTML = `
                    <div class="membership-status regular">
                        <div class="status-header">
                            <span class="status-icon">👤</span>
                            <div class="status-info">
                                <h5>Regular User</h5>
                                <p class="status-description">No Pro key configured</p>
                            </div>
                        </div>
                        <div class="membership-actions">
                            <button id="upgradeFromMembership" class="btn btn-primary btn-sm">
                                🚀 Upgrade to Pro
                            </button>
                        </div>
                    </div>
                `;
                
                // Add event listener for upgrade button
                const upgradeBtn = document.getElementById('upgradeFromMembership');
                if (upgradeBtn) {
                    upgradeBtn.addEventListener('click', () => {
                        this.controller.uiManager.showSection('upgradeSection');
                    });
                }
                return;
            }
            
            if (!detailedStatus.isPro) {
                // Pro key exists but not valid (expired or invalid)
                const isExpired = detailedStatus.expired;
                
                membershipContent.innerHTML = `
                    <div class="membership-status ${isExpired ? 'expired' : 'invalid'}">
                        <div class="status-header">
                            <span class="status-icon">${isExpired ? '⏰' : '❌'}</span>
                            <div class="status-info">
                                <h5>${isExpired ? 'Membership Expired' : 'Invalid Pro Key'}</h5>
                                <p class="status-description">${detailedStatus.message}</p>
                            </div>
                        </div>
                        <div class="membership-actions">
                            <button id="renewMembership" class="btn btn-primary btn-sm">
                                🔄 ${isExpired ? 'Renew Membership' : 'Get Valid Pro Key'}
                            </button>
                        </div>
                    </div>
                `;
                
                // Add event listener for renew button
                const renewBtn = document.getElementById('renewMembership');
                if (renewBtn) {
                    renewBtn.addEventListener('click', () => {
                        this.controller.uiManager.showSection('upgradeSection');
                    });
                }
                return;
            }
            
            // User has valid pro membership
            const details = detailedStatus.membershipDetails;
            const isLegacy = detailedStatus.legacy;
            
            membershipContent.innerHTML = `
                <div class="membership-status active">
                    <div class="status-header">
                        <span class="status-icon">✅</span>
                        <div class="status-info">
                            <h5>Pro Member Active</h5>
                            <p class="status-description">${detailedStatus.message}</p>
                        </div>
                    </div>
                    
                    ${!isLegacy && details ? `
                        <div class="membership-details">
                            <div class="detail-item">
                                <label>Membership Tier:</label>
                                <span class="tier-badge">${(details.tier || 'pro').toUpperCase()}</span>
                            </div>
                            <div class="detail-item">
                                <label>Days Remaining:</label>
                                <span class="days-remaining ${details.daysRemaining <= 7 ? 'warning' : ''}">${details.daysRemaining} days</span>
                            </div>
                        </div>
                    ` : `
                        <div class="legacy-notice">
                            <span class="legacy-icon">📜</span>
                            <span class="legacy-text">Legacy Pro Key - No expiration tracking</span>
                        </div>
                    `}
                    
                    <div class="membership-actions">
                        <button id="manageMembershipKey" class="btn btn-outline btn-sm">
                            🔑 Manage Key
                        </button>
                    </div>
                </div>
            `;
            
            // Add event listener for manage key button
            const manageKeyBtn = document.getElementById('manageMembershipKey');
            if (manageKeyBtn) {
                manageKeyBtn.addEventListener('click', () => {
                    this.controller.uiManager.showKeyManagementModal();
                });
            }
            
        } catch (error) {
            console.error('Error loading membership info:', error);
            membershipContent.innerHTML = `
                <div class="membership-status error">
                    <div class="status-header">
                        <span class="status-icon">❌</span>
                        <div class="status-info">
                            <h5>Error Loading Membership</h5>
                            <p class="status-description">Unable to load membership information</p>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Load Telegram settings
     */
    async loadTelegramSettings() {
        const telegramContent = document.getElementById('telegramSettingsContent');
        if (!telegramContent) return;

        try {
            // Run membership check for settings access
            await runDailyMembershipCheck();

            const proStatus = await checkProStatus();

            if (!proStatus.isPro) {
                // Show expiration message if membership expired
                if (proStatus.expired) {
                    telegramContent.innerHTML = `
                        <div class="pro-upgrade-notice">
                            <div class="upgrade-icon">⏰</div>
                            <h4>Membership Expired</h4>
                            <p>Your Pro membership has expired. Renew to continue using Telegram integration.</p>
                            <button id="telegramRenewBtn" class="btn btn-primary btn-sm">
                                Renew Membership
                            </button>
                        </div>
                    `;

                    const renewBtn = document.getElementById('telegramRenewBtn');
                    if (renewBtn) {
                        renewBtn.addEventListener('click', () => {
                            this.controller.uiManager.showSection('upgradeSection');
                        });
                    }
                } else {
                    // Regular user - show upgrade notice
                    telegramContent.innerHTML = `
                        <div class="pro-upgrade-notice">
                            <div class="upgrade-icon">🚀</div>
                            <h4>Pro Feature</h4>
                            <p>Telegram integration is available for Pro users. Upgrade to automatically send analysis results to your Telegram chat.</p>
                            <button id="telegramUpgradeBtn" class="btn btn-primary btn-sm">
                                Upgrade to Pro
                            </button>
                        </div>
                    `;

                    const upgradeBtn = document.getElementById('telegramUpgradeBtn');
                    if (upgradeBtn) {
                        upgradeBtn.addEventListener('click', () => {
                            this.controller.uiManager.showSection('upgradeSection');
                        });
                    }
                }
                return;
            }

            // User is pro, check if Telegram is configured
            const isConfigured = await isTelegramConfigured();

            if (!isConfigured) {
                // Show setup form
                telegramContent.innerHTML = `
                    <div class="integration-setup">
                        <div class="setup-header">
                            <h4>📱 Setup Telegram Integration</h4>
                            <p>Send analysis results directly to your Telegram chat</p>
                        </div>
                        <div class="setup-form">
                            <div class="form-group">
                                <label for="telegramBotToken">Bot Token:</label>
                                <input type="password" id="telegramBotToken" placeholder="Enter your bot token">
                                <small>Create a bot with @BotFather on Telegram</small>
                            </div>
                            <div class="form-group">
                                <label for="telegramChatId">Chat ID:</label>
                                <input type="text" id="telegramChatId" placeholder="Enter chat ID">
                                <small>Your chat ID or group chat ID</small>
                            </div>
                            <div class="form-actions">
                                <button id="saveTelegramSettings" class="btn btn-primary">
                                    💾 Save Settings
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                this.setupTelegramEventListeners();
            } else {
                // Show configured status
                const settings = await getTelegramSettings();
                const maskedToken = await getMaskedBotToken();
                const autoSendSettings = await getTelegramAutoSendSettings();

                telegramContent.innerHTML = `
                    <div class="integration-configured">
                        <div class="config-header">
                            <h4>📱 Telegram Integration</h4>
                            <span class="status-badge active">✅ Configured</span>
                        </div>
                        <div class="config-details">
                            <div class="config-item">
                                <label>Bot Token:</label>
                                <span class="masked-value">${maskedToken}</span>
                            </div>
                            <div class="config-item">
                                <label>Chat ID:</label>
                                <span class="masked-value">${settings.chatId}</span>
                            </div>
                        </div>
                        <div class="auto-send-section">
                            <div class="auto-send-header">
                                <h5>🚀 Auto-Send Analysis Results</h5>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="telegramAutoSend" ${autoSendSettings.enabled ? 'checked' : ''}>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            ${autoSendSettings.failureCount > 0 ? `
                                <div class="auto-send-warning">
                                    <span class="warning-icon">⚠️</span>
                                    <span class="warning-text">${autoSendSettings.failureCount} recent failure(s). ${autoSendSettings.failureCount >= 3 ? 'Auto-send has been disabled.' : ''}</span>
                                </div>
                            ` : ''}
                            ${autoSendSettings.lastSent ? `
                                <div class="auto-send-info">
                                    <span class="info-icon">📤</span>
                                    <span class="info-text">Last sent: ${new Date(autoSendSettings.lastSent).toLocaleString()}</span>
                                </div>
                            ` : ''}
                        </div>
                        <div class="config-actions">
                            <button id="testTelegramConnection" class="btn btn-outline btn-sm">🔧 Test Connection</button>
                            <button id="editTelegramSettings" class="btn btn-outline btn-sm">✏️ Edit Settings</button>
                            <button id="clearTelegramSettings" class="btn btn-danger btn-sm">🗑️ Clear Settings</button>
                        </div>
                    </div>
                `;

                this.setupTelegramEventListeners();
            }

        } catch (error) {
            console.error('Error loading Telegram settings:', error);
            telegramContent.innerHTML = `
                <div class="error-state">
                    <h4>❌ Error Loading Settings</h4>
                    <p>Unable to load Telegram integration settings</p>
                    <button onclick="location.reload()" class="btn btn-outline btn-sm">🔄 Retry</button>
                </div>
            `;
        }
    }

    /**
     * Setup Telegram event listeners
     */
    setupTelegramEventListeners() {
        // Save settings
        const saveBtn = document.getElementById('saveTelegramSettings');
        if (saveBtn) {
            saveBtn.addEventListener('click', async () => {
                await this.saveTelegramSettings();
            });
        }

        // Test connection
        const testBtn = document.getElementById('testTelegramConnection');
        if (testBtn) {
            testBtn.addEventListener('click', async () => {
                await this.testTelegramConnection();
            });
        }

        // Edit settings
        const editBtn = document.getElementById('editTelegramSettings');
        if (editBtn) {
            editBtn.addEventListener('click', async () => {
                await this.editTelegramSettings();
            });
        }

        // Clear settings
        const clearBtn = document.getElementById('clearTelegramSettings');
        if (clearBtn) {
            clearBtn.addEventListener('click', async () => {
                await this.clearTelegramSettings();
            });
        }

        // Auto-send toggle
        const autoSendToggle = document.getElementById('telegramAutoSend');
        if (autoSendToggle) {
            autoSendToggle.addEventListener('change', async (e) => {
                await saveTelegramAutoSendSettings({ enabled: e.target.checked });
                this.controller.uiManager.showSuccess(`Telegram auto-send ${e.target.checked ? 'enabled' : 'disabled'}`);
            });
        }
    }

    /**
     * Save Telegram settings
     */
    async saveTelegramSettings() {
        const botToken = document.getElementById('telegramBotToken').value.trim();
        const chatId = document.getElementById('telegramChatId').value.trim();
        const saveBtn = document.getElementById('saveTelegramSettings');

        if (!botToken || !chatId) {
            this.controller.uiManager.showError('Please fill in all fields');
            return;
        }

        try {
            saveBtn.disabled = true;
            saveBtn.innerHTML = '⏳ Saving...';

            const success = await saveTelegramSettings(botToken, chatId);

            if (success) {
                this.controller.uiManager.showSuccess('Telegram settings saved successfully!');
                await this.loadTelegramSettings(); // Refresh the display
            } else {
                this.controller.uiManager.showError('Failed to save Telegram settings');
            }

        } catch (error) {
            console.error('Error saving Telegram settings:', error);
            this.controller.uiManager.showError('Failed to save Telegram settings');
        } finally {
            saveBtn.disabled = false;
            saveBtn.innerHTML = '💾 Save Settings';
        }
    }

    /**
     * Test Telegram connection
     */
    async testTelegramConnection() {
        const testBtn = document.getElementById('testTelegramConnection');

        try {
            testBtn.disabled = true;
            testBtn.innerHTML = '⏳ Testing...';

            const result = await testTelegramConnection();

            if (result.success) {
                this.controller.uiManager.showSuccess('✅ Telegram connection successful!');
            } else {
                this.controller.uiManager.showError(`❌ Connection failed: ${result.error}`);
            }

        } catch (error) {
            console.error('Error testing Telegram connection:', error);
            this.controller.uiManager.showError('Failed to test connection');
        } finally {
            testBtn.disabled = false;
            testBtn.innerHTML = '🔧 Test Connection';
        }
    }

    /**
     * Edit Telegram settings
     */
    async editTelegramSettings() {
        // Switch back to setup form
        await this.loadSettingsContent();
    }

    /**
     * Clear Telegram settings
     */
    async clearTelegramSettings() {
        if (!confirm('Are you sure you want to clear your Telegram settings? This cannot be undone.')) {
            return;
        }

        try {
            await clearTelegramSettings();
            this.controller.uiManager.showSuccess('Telegram settings cleared');
            await this.loadTelegramSettings(); // Refresh the display
        } catch (error) {
            console.error('Error clearing Telegram settings:', error);
            this.controller.uiManager.showError('Failed to clear settings');
        }
    }

    /**
     * Load Discord settings
     */
    async loadDiscordSettings() {
        const discordContent = document.getElementById('discordSettingsContent');
        if (!discordContent) return;

        try {
            const proStatus = await checkProStatus();

            if (!proStatus.isPro) {
                discordContent.innerHTML = `
                    <div class="pro-upgrade-notice">
                        <div class="upgrade-icon">🚀</div>
                        <h4>Pro Feature</h4>
                        <p>Discord integration is available for Pro users. Upgrade to automatically send analysis results to your Discord channel.</p>
                        <button id="discordUpgradeBtn" class="btn btn-primary btn-sm">
                            Upgrade to Pro
                        </button>
                    </div>
                `;

                const upgradeBtn = document.getElementById('discordUpgradeBtn');
                if (upgradeBtn) {
                    upgradeBtn.addEventListener('click', () => {
                        this.controller.uiManager.showSection('upgradeSection');
                    });
                }
                return;
            }

            // User is pro, check if Discord is configured
            const isConfigured = await isDiscordConfigured();

            if (!isConfigured) {
                // Show setup form
                discordContent.innerHTML = `
                    <div class="integration-setup">
                        <div class="setup-header">
                            <h4>💬 Setup Discord Integration</h4>
                            <p>Send analysis results directly to your Discord channel</p>
                        </div>
                        <div class="setup-form">
                            <div class="form-group">
                                <label for="discordWebhookUrl">Webhook URL:</label>
                                <input type="password" id="discordWebhookUrl" placeholder="Enter your Discord webhook URL">
                                <small>Create a webhook in your Discord channel settings</small>
                            </div>
                            <div class="form-actions">
                                <button id="saveDiscordSettings" class="btn btn-primary">
                                    💾 Save Settings
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                this.setupDiscordEventListeners();
            } else {
                // Show configured status
                const settings = await getDiscordSettings();
                const maskedUrl = await getMaskedWebhookUrl();
                const autoSendSettings = await getDiscordAutoSendSettings();

                discordContent.innerHTML = `
                    <div class="integration-configured">
                        <div class="config-header">
                            <h4>💬 Discord Integration</h4>
                            <span class="status-badge active">✅ Configured</span>
                        </div>
                        <div class="config-details">
                            <div class="config-item">
                                <label>Webhook URL:</label>
                                <span class="masked-value">${maskedUrl}</span>
                            </div>
                        </div>
                        <div class="auto-send-section">
                            <div class="auto-send-header">
                                <h5>🚀 Auto-Send Analysis Results</h5>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="discordAutoSend" ${autoSendSettings.enabled ? 'checked' : ''}>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            ${autoSendSettings.failureCount > 0 ? `
                                <div class="auto-send-warning">
                                    <span class="warning-icon">⚠️</span>
                                    <span class="warning-text">${autoSendSettings.failureCount} recent failure(s). ${autoSendSettings.failureCount >= 3 ? 'Auto-send has been disabled.' : ''}</span>
                                </div>
                            ` : ''}
                            ${autoSendSettings.lastSent ? `
                                <div class="auto-send-info">
                                    <span class="info-icon">📤</span>
                                    <span class="info-text">Last sent: ${new Date(autoSendSettings.lastSent).toLocaleString()}</span>
                                </div>
                            ` : ''}
                        </div>
                        <div class="config-actions">
                            <button id="testDiscordConnection" class="btn btn-outline btn-sm">🔧 Test Connection</button>
                            <button id="editDiscordSettings" class="btn btn-outline btn-sm">✏️ Edit Settings</button>
                            <button id="clearDiscordSettings" class="btn btn-danger btn-sm">🗑️ Clear Settings</button>
                        </div>
                    </div>
                `;

                this.setupDiscordEventListeners();
            }

        } catch (error) {
            console.error('Error loading Discord settings:', error);
            discordContent.innerHTML = `
                <div class="error-state">
                    <h4>❌ Error Loading Settings</h4>
                    <p>Unable to load Discord integration settings</p>
                    <button onclick="location.reload()" class="btn btn-outline btn-sm">🔄 Retry</button>
                </div>
            `;
        }
    }

    /**
     * Setup Discord event listeners
     */
    setupDiscordEventListeners() {
        // Save settings
        const saveBtn = document.getElementById('saveDiscordSettings');
        if (saveBtn) {
            saveBtn.addEventListener('click', async () => {
                await this.saveDiscordSettings();
            });
        }

        // Test connection
        const testBtn = document.getElementById('testDiscordConnection');
        if (testBtn) {
            testBtn.addEventListener('click', async () => {
                await this.testDiscordConnection();
            });
        }

        // Edit settings
        const editBtn = document.getElementById('editDiscordSettings');
        if (editBtn) {
            editBtn.addEventListener('click', async () => {
                await this.editDiscordSettings();
            });
        }

        // Clear settings
        const clearBtn = document.getElementById('clearDiscordSettings');
        if (clearBtn) {
            clearBtn.addEventListener('click', async () => {
                await this.clearDiscordSettings();
            });
        }

        // Auto-send toggle
        const autoSendToggle = document.getElementById('discordAutoSend');
        if (autoSendToggle) {
            autoSendToggle.addEventListener('change', async (e) => {
                await saveDiscordAutoSendSettings({ enabled: e.target.checked });
                this.controller.uiManager.showSuccess(`Discord auto-send ${e.target.checked ? 'enabled' : 'disabled'}`);
            });
        }
    }

    /**
     * Save Discord settings
     */
    async saveDiscordSettings() {
        const webhookUrl = document.getElementById('discordWebhookUrl').value.trim();
        const saveBtn = document.getElementById('saveDiscordSettings');

        if (!webhookUrl) {
            this.controller.uiManager.showError('Please enter a webhook URL');
            return;
        }

        try {
            saveBtn.disabled = true;
            saveBtn.innerHTML = '⏳ Saving...';

            const success = await saveDiscordSettings(webhookUrl);

            if (success) {
                this.controller.uiManager.showSuccess('Discord settings saved successfully!');
                await this.loadDiscordSettings(); // Refresh the display
            } else {
                this.controller.uiManager.showError('Failed to save Discord settings');
            }

        } catch (error) {
            console.error('Error saving Discord settings:', error);
            this.controller.uiManager.showError('Failed to save Discord settings');
        } finally {
            saveBtn.disabled = false;
            saveBtn.innerHTML = '💾 Save Settings';
        }
    }

    /**
     * Test Discord connection
     */
    async testDiscordConnection() {
        const testBtn = document.getElementById('testDiscordConnection');

        try {
            testBtn.disabled = true;
            testBtn.innerHTML = '⏳ Testing...';

            const result = await testDiscordWebhook();

            if (result.success) {
                this.controller.uiManager.showSuccess('✅ Discord connection successful!');
            } else {
                this.controller.uiManager.showError(`❌ Connection failed: ${result.error}`);
            }

        } catch (error) {
            console.error('Error testing Discord connection:', error);
            this.controller.uiManager.showError('Failed to test connection');
        } finally {
            testBtn.disabled = false;
            testBtn.innerHTML = '🔧 Test Connection';
        }
    }

    /**
     * Edit Discord settings
     */
    async editDiscordSettings() {
        await this.loadSettingsContent();
    }

    /**
     * Clear Discord settings
     */
    async clearDiscordSettings() {
        if (!confirm('Are you sure you want to clear your Discord settings? This cannot be undone.')) {
            return;
        }

        try {
            await clearDiscordSettings();
            this.controller.uiManager.showSuccess('Discord settings cleared');
            await this.loadDiscordSettings();
        } catch (error) {
            console.error('Error clearing Discord settings:', error);
            this.controller.uiManager.showError('Failed to clear settings');
        }
    }

    /**
     * Handle custom analysis click (Pro feature check)
     */
    async handleCustomAnalysisClick() {
        try {
            // Run daily membership check
            await runDailyMembershipCheck();

            const proStatus = await checkProStatus();

            if (proStatus.isPro) {
                // Show expiration warning if applicable
                if (proStatus.expirationWarning) {
                    this.controller.uiManager.showMembershipWarning(proStatus.expirationWarning);
                }

                this.controller.uiManager.showSection('customAnalysisSection');
            } else {
                // Show upgrade section with specific reason
                this.controller.uiManager.showSection('upgradeSection');

                // Set upgrade reason for better UX
                const upgradeReason = document.getElementById('upgradeReason');
                if (upgradeReason) {
                    upgradeReason.textContent = 'Custom Analysis is a Pro feature. Upgrade to create your own analysis prompts and get more detailed insights.';
                }
            }
        } catch (error) {
            console.error('Error checking pro status for custom analysis:', error);
            this.controller.uiManager.showError('Unable to verify Pro status. Please try again.');
        }
    }

    /**
     * Handle prompt manager click (Pro feature check)
     */
    async handlePromptManagerClick() {
        try {
            // Run daily membership check
            await runDailyMembershipCheck();

            const proStatus = await checkProStatus();

            if (proStatus.isPro) {
                // Show expiration warning if applicable
                if (proStatus.expirationWarning) {
                    this.controller.uiManager.showMembershipWarning(proStatus.expirationWarning);
                }

                this.controller.uiManager.showSection('promptManagementSection');
                await this.controller.promptUIManager.loadPromptManagement();
            } else {
                // Show upgrade section with specific reason
                this.controller.uiManager.showSection('upgradeSection');

                // Set upgrade reason for better UX
                const upgradeReason = document.getElementById('upgradeReason');
                if (upgradeReason) {
                    upgradeReason.textContent = 'Prompt Management is a Pro feature. Upgrade to save, organize, and reuse your custom analysis prompts.';
                }
            }
        } catch (error) {
            console.error('Error checking pro status for prompt management:', error);
            this.controller.uiManager.showError('Unable to verify Pro status. Please try again.');
        }
    }

    /**
     * Handle Pro key validation
     */
    async handleProKeyValidation() {
        const proKeyInput = document.getElementById('proKeyInput');
        const proKeyStatus = document.getElementById('proKeyStatus');
        const validateBtn = document.getElementById('validateProKey');

        const proKey = proKeyInput.value.trim();

        if (!proKey) {
            this.updateProKeyStatus('invalid', 'Please enter a pro key');
            return;
        }

        try {
            validateBtn.disabled = true;
            validateBtn.textContent = '⏳ Validating...';
            this.updateProKeyStatus('validating', 'Validating pro key...');

            // Save the key and validate
            const success = await setProKey(proKey);

            if (success) {
                // Refresh pro status to get updated information
                const proStatus = await refreshProStatus();

                if (proStatus.isPro) {
                    this.updateProKeyStatus('valid', `✅ Valid Pro Key! Welcome ${proStatus.membershipDetails?.name || 'Pro User'}!`);

                    // Hide the key input section and show success
                    setTimeout(() => {
                        this.controller.uiManager.showSection('actionsSection');
                        this.controller.uiManager.showSuccess('Pro key validated successfully! You now have access to all Pro features.');
                    }, 2000);
                } else {
                    this.updateProKeyStatus('invalid', proStatus.message || 'Invalid pro key');
                }
            } else {
                this.updateProKeyStatus('invalid', 'Failed to save pro key');
            }
        } catch (error) {
            console.error('Error validating pro key:', error);
            this.updateProKeyStatus('invalid', 'Error validating pro key');
        } finally {
            validateBtn.disabled = false;
            validateBtn.textContent = '🔑 Validate Pro Key';
        }
    }

    /**
     * Update Pro key status display
     */
    updateProKeyStatus(status, message) {
        const statusElement = document.getElementById('proKeyStatus');
        if (!statusElement) return;

        const statusText = statusElement.querySelector('.status-text');
        const statusIcon = statusElement.querySelector('.status-icon');

        if (statusText) statusText.textContent = message;

        // Update status classes and icon
        statusElement.className = `pro-key-status ${status}`;

        if (statusIcon) {
            switch (status) {
                case 'valid':
                    statusIcon.textContent = '✅';
                    break;
                case 'invalid':
                    statusIcon.textContent = '❌';
                    break;
                case 'validating':
                    statusIcon.textContent = '⏳';
                    break;
                default:
                    statusIcon.textContent = 'ℹ️';
            }
        }
    }
}
