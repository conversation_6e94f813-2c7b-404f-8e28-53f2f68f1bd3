/**
 * Prompt Management System
 * Handles CRUD operations for user prompts with tags, search, and pinning
 */

export class PromptManager {
    constructor() {
        this.storageKey = 'hustleplugPrompts';
        this.init();
    }

    async init() {
        // Ensure default prompts exist
        await this.ensureDefaultPrompts();
    }

    /**
     * Get all prompts from storage
     */
    async getPrompts() {
        try {
            const result = await chrome.storage.local.get([this.storageKey]);
            return result[this.storageKey] || [];
        } catch (error) {
            console.error('Error loading prompts:', error);
            return [];
        }
    }

    /**
     * Save a new prompt or update existing one
     */
    async savePrompt(prompt) {
        try {
            const prompts = await this.getPrompts();
            const now = new Date().toISOString();
            
            if (prompt.id) {
                // Update existing prompt
                const index = prompts.findIndex(p => p.id === prompt.id);
                if (index !== -1) {
                    prompts[index] = {
                        ...prompt,
                        updatedAt: now
                    };
                }
            } else {
                // Create new prompt
                const newPrompt = {
                    id: this.generateId(),
                    title: prompt.title,
                    content: prompt.content,
                    tags: prompt.tags || [],
                    isPinned: prompt.isPinned || false,
                    createdAt: now,
                    updatedAt: now,
                    usageCount: 0
                };
                prompts.unshift(newPrompt); // Add to beginning
            }

            await chrome.storage.local.set({ [this.storageKey]: prompts });
            return true;
        } catch (error) {
            console.error('Error saving prompt:', error);
            return false;
        }
    }

    /**
     * Delete a prompt by ID
     */
    async deletePrompt(promptId) {
        try {
            const prompts = await this.getPrompts();
            const filteredPrompts = prompts.filter(p => p.id !== promptId);
            await chrome.storage.local.set({ [this.storageKey]: filteredPrompts });
            return true;
        } catch (error) {
            console.error('Error deleting prompt:', error);
            return false;
        }
    }

    /**
     * Toggle pin status of a prompt
     */
    async togglePin(promptId) {
        try {
            const prompts = await this.getPrompts();
            const prompt = prompts.find(p => p.id === promptId);
            if (prompt) {
                prompt.isPinned = !prompt.isPinned;
                prompt.updatedAt = new Date().toISOString();
                await chrome.storage.local.set({ [this.storageKey]: prompts });
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error toggling pin:', error);
            return false;
        }
    }

    /**
     * Increment usage count for a prompt
     */
    async incrementUsage(promptId) {
        try {
            const prompts = await this.getPrompts();
            const prompt = prompts.find(p => p.id === promptId);
            if (prompt) {
                prompt.usageCount = (prompt.usageCount || 0) + 1;
                prompt.lastUsed = new Date().toISOString();
                await chrome.storage.local.set({ [this.storageKey]: prompts });
            }
        } catch (error) {
            console.error('Error incrementing usage:', error);
        }
    }

    /**
     * Search prompts by title, content, or tags
     */
    async searchPrompts(query) {
        const prompts = await this.getPrompts();
        if (!query) return prompts;

        const searchTerm = query.toLowerCase();
        return prompts.filter(prompt => 
            prompt.title.toLowerCase().includes(searchTerm) ||
            prompt.content.toLowerCase().includes(searchTerm) ||
            prompt.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
    }

    /**
     * Get prompts filtered by tag
     */
    async getPromptsByTag(tag) {
        const prompts = await this.getPrompts();
        return prompts.filter(prompt => prompt.tags.includes(tag));
    }

    /**
     * Get all unique tags
     */
    async getAllTags() {
        const prompts = await this.getPrompts();
        const tagSet = new Set();
        prompts.forEach(prompt => {
            prompt.tags.forEach(tag => tagSet.add(tag));
        });
        return Array.from(tagSet).sort();
    }

    /**
     * Get pinned prompts
     */
    async getPinnedPrompts() {
        const prompts = await this.getPrompts();
        return prompts.filter(prompt => prompt.isPinned);
    }

    /**
     * Sort prompts by various criteria
     */
    sortPrompts(prompts, sortBy = 'recent') {
        switch (sortBy) {
            case 'alphabetical':
                return prompts.sort((a, b) => a.title.localeCompare(b.title));
            case 'usage':
                return prompts.sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0));
            case 'recent':
            default:
                return prompts.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
        }
    }

    /**
     * Generate unique ID for prompts
     */
    generateId() {
        return 'prompt_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Ensure default prompts exist for new users
     */
    async ensureDefaultPrompts() {
        const prompts = await this.getPrompts();
        if (prompts.length === 0) {
            const defaultPrompts = [
                {
                    id: this.generateId(),
                    title: 'SEO Analysis',
                    content: 'Analyze the SEO quality of this content. Focus on keyword density, meta information, content structure, and provide specific improvement recommendations.',
                    tags: ['SEO', 'Marketing', 'Analysis'],
                    isPinned: true,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    usageCount: 0
                },
                {
                    id: this.generateId(),
                    title: 'Content Summary',
                    content: 'Provide a concise summary of this content, highlighting the main points and key takeaways.',
                    tags: ['Summary', 'Content'],
                    isPinned: false,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    usageCount: 0
                },
                {
                    id: this.generateId(),
                    title: 'Security Review',
                    content: 'Review this content for potential security issues, vulnerabilities, or privacy concerns. Provide actionable recommendations.',
                    tags: ['Security', 'Review', 'Privacy'],
                    isPinned: false,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    usageCount: 0
                }
            ];

            await chrome.storage.local.set({ [this.storageKey]: defaultPrompts });
        }
    }

    /**
     * Export prompts as JSON
     */
    async exportPrompts() {
        const prompts = await this.getPrompts();
        return JSON.stringify(prompts, null, 2);
    }

    /**
     * Import prompts from JSON
     */
    async importPrompts(jsonData) {
        try {
            const importedPrompts = JSON.parse(jsonData);
            if (!Array.isArray(importedPrompts)) {
                throw new Error('Invalid format: Expected array of prompts');
            }

            // Validate prompt structure
            const validPrompts = importedPrompts.filter(prompt => 
                prompt.title && prompt.content
            ).map(prompt => ({
                ...prompt,
                id: this.generateId(), // Generate new IDs to avoid conflicts
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                usageCount: 0
            }));

            const existingPrompts = await this.getPrompts();
            const allPrompts = [...existingPrompts, ...validPrompts];
            
            await chrome.storage.local.set({ [this.storageKey]: allPrompts });
            return { success: true, imported: validPrompts.length };
        } catch (error) {
            console.error('Error importing prompts:', error);
            return { success: false, error: error.message };
        }
    }



    /**
     * Get a specific prompt by ID
     */
    async getPromptById(promptId) {
        try {
            const prompts = await this.getPrompts();
            return prompts.find(p => p.id === promptId) || null;
        } catch (error) {
            console.error('Error getting prompt by ID:', error);
            return null;
        }
    }

    /**
     * Copy prompt content to clipboard
     */
    async copyPromptContent(promptId) {
        try {
            const prompt = await this.getPromptById(promptId);
            if (!prompt) {
                throw new Error('Prompt not found');
            }

            // Use the modern clipboard API if available
            if (navigator.clipboard && navigator.clipboard.writeText) {
                await navigator.clipboard.writeText(prompt.content);
                return { success: true, content: prompt.content };
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = prompt.content;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                const success = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (success) {
                    return { success: true, content: prompt.content };
                } else {
                    throw new Error('Failed to copy to clipboard');
                }
            }
        } catch (error) {
            console.error('Error copying prompt content:', error);
            return { success: false, error: error.message };
        }
    }
}

// Export singleton instance
export const promptManager = new PromptManager(); 