/**
 * Analysis Manager
 * Handles all analysis operations (selection, page, custom) and result processing
 */
import { BaseManager } from '../core/BaseManager.js';

export class AnalysisManager extends BaseManager {
    constructor(controller) {
        super(controller);
    }

    async init() {
        await super.init();
        // Analysis Manager doesn't need async initialization
    }

    /**
     * Analyze selected text from the current tab
     */
    async analyzeSelection() {
        try {
            this.controller.uiManager.showSection('loadingSection');
            
            // Get selected text from active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: () => window.getSelection().toString()
            });
            
            const selectedText = results[0].result;
            
            if (!selectedText || selectedText.trim().length === 0) {
                this.controller.uiManager.showError('No text selected. Please select some text and try again.');
                this.controller.uiManager.showSection('actionsSection');
                return;
            }

            const prompt = `Please analyze the following selected text and provide insights, key points, and any relevant analysis:

Selected Text:
${selectedText}

Please provide a comprehensive analysis including:
1. Summary of the content
2. Key insights and takeaways
3. Any important details or patterns
4. Recommendations or next steps if applicable`;

            await this.performAnalysis(prompt, 'Text Selection Analysis');
            
        } catch (error) {
            console.error('Error analyzing selection:', error);
            this.controller.uiManager.showError('Failed to analyze selected text');
            this.controller.uiManager.showSection('actionsSection');
        }
    }

    /**
     * Analyze selected text with provided text (from content script)
     */
    async analyzeSelectionWithText(selectedText) {
        try {
            if (!selectedText || selectedText.trim().length === 0) {
                this.controller.uiManager.showError('No text provided for analysis.');
                return;
            }

            this.controller.uiManager.showSection('loadingSection');

            const prompt = `Please analyze the following selected text and provide insights, key points, and any relevant analysis:

Selected Text:
${selectedText}

Please provide a comprehensive analysis including:
1. Summary of the content
2. Key insights and takeaways
3. Any important details or patterns
4. Recommendations or next steps if applicable`;

            await this.performAnalysis(prompt, 'Text Selection Analysis');
            
        } catch (error) {
            console.error('Error analyzing provided text:', error);
            this.controller.uiManager.showError('Failed to analyze selected text');
            this.controller.uiManager.showSection('actionsSection');
        }
    }

    /**
     * Analyze the entire page content
     */
    async analyzePage() {
        try {
            this.controller.uiManager.showSection('loadingSection');
            
            // Get page content from active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: () => {
                    // Extract meaningful content from the page
                    const title = document.title;
                    const url = window.location.href;
                    const metaDescription = document.querySelector('meta[name="description"]')?.content || '';
                    
                    // Get main content (try to avoid navigation, ads, etc.)
                    const contentSelectors = [
                        'main',
                        'article',
                        '.content',
                        '.post-content',
                        '.entry-content',
                        '#content',
                        '.main-content'
                    ];
                    
                    let mainContent = '';
                    for (const selector of contentSelectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            mainContent = element.innerText;
                            break;
                        }
                    }
                    
                    // Fallback to body content if no main content found
                    if (!mainContent) {
                        mainContent = document.body.innerText;
                    }
                    
                    // Limit content length to avoid API limits
                    const maxLength = 8000;
                    if (mainContent.length > maxLength) {
                        mainContent = mainContent.substring(0, maxLength) + '...';
                    }
                    
                    return {
                        title,
                        url,
                        metaDescription,
                        content: mainContent
                    };
                }
            });
            
            const pageData = results[0].result;
            
            const prompt = `Please analyze the following webpage and provide comprehensive insights:

Page Title: ${pageData.title}
URL: ${pageData.url}
Meta Description: ${pageData.metaDescription}

Page Content:
${pageData.content}

Please provide a detailed analysis including:
1. Summary of the page content and purpose
2. Key topics and themes discussed
3. Important information and insights
4. Content quality and credibility assessment
5. Any actionable takeaways or recommendations
6. Potential use cases or applications of this information`;

            await this.performAnalysis(prompt, 'Full Page Analysis');
            
        } catch (error) {
            console.error('Error analyzing page:', error);
            this.controller.uiManager.showError('Failed to analyze page content');
            this.controller.uiManager.showSection('actionsSection');
        }
    }

    /**
     * Analyze page with provided data (from content script)
     */
    async analyzePageWithData(pageData) {
        try {
            if (!pageData || !pageData.content) {
                this.controller.uiManager.showError('No page content available for analysis.');
                return;
            }

            this.controller.uiManager.showSection('loadingSection');

            const prompt = `Please analyze the following webpage and provide comprehensive insights:

Page Title: ${pageData.title}
URL: ${pageData.url}

Page Content:
${pageData.content}

Please provide a detailed analysis including:
1. Summary of the page content and purpose
2. Key topics and themes discussed
3. Important information and insights
4. Content quality and credibility assessment
5. Any actionable takeaways or recommendations`;

            await this.performAnalysis(prompt, 'Full Page Analysis');
            
        } catch (error) {
            console.error('Error analyzing page:', error);
            this.controller.uiManager.showError('Failed to analyze page');
            this.controller.uiManager.showSection('actionsSection');
        }
    }

    /**
     * Run custom analysis with user-provided prompt
     */
    async runCustomAnalysis() {
        try {
            const prompt = document.getElementById('customPrompt').value.trim();
            const dataSource = document.querySelector('input[name="dataSource"]:checked').value;
            
            if (!prompt) {
                this.controller.uiManager.showError('Please enter an analysis prompt');
                return;
            }
            
            this.controller.uiManager.showSection('loadingSection');
            
            let data = '';
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (dataSource === 'selection') {
                const results = await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    function: () => window.getSelection().toString()
                });
                data = results[0].result;
                
                if (!data || data.trim().length === 0) {
                    this.controller.uiManager.showError('No text selected. Please select some text or choose "Full Page" option.');
                    this.controller.uiManager.showSection('customForm');
                    return;
                }
            } else {
                const results = await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    function: () => {
                        const title = document.title;
                        const content = document.body.innerText.substring(0, 8000);
                        return `Title: ${title}\n\nContent: ${content}`;
                    }
                });
                data = results[0].result;
            }
            
            const fullPrompt = `${prompt}

Data to analyze:
${data}`;
            
            await this.performAnalysis(fullPrompt, 'Custom Analysis');
            
        } catch (error) {
            console.error('Error running custom analysis:', error);
            this.controller.uiManager.showError('Failed to run custom analysis');
            this.controller.uiManager.showSection('customForm');
        }
    }

    /**
     * Core analysis function - sends request to background script
     */
    async performAnalysis(prompt, analysisType) {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'performAnalysis',
                data: {
                    prompt,
                    analysisType
                }
            });

            if (response.error) {
                throw new Error(response.error);
            }

            this.controller.currentAnalysis = {
                type: analysisType,
                prompt: prompt,
                result: response.result,
                timestamp: new Date().toISOString()
            };
            
            // Save the new analysis
            await this.controller.dataManager.saveAnalysis(analysisType, response.result);

            this.displayResults(response.result, analysisType);
            
            // Handle auto-send after successful analysis
            await this.controller.autoSendManager.handleAutoSend(analysisType, response.result);
            
        } catch (error) {
            console.error('Analysis error:', error);
            this.controller.uiManager.showError(`Analysis failed: ${error.message}`);
            this.controller.uiManager.showSection('actionsSection');
        }
    }

    /**
     * Display analysis results in the UI
     */
    displayResults(result, analysisType, date = new Date()) {
        this.controller.currentAnalysis = {
            result: result,
            type: analysisType,
            timestamp: date.toISOString(),
        };

        const resultsContainer = document.getElementById('analysisResults');
        if (!resultsContainer) {
            console.error('Results container not found');
            return;
        }
        
        let content = '';
        if (typeof result === 'string') {
            content = result;
        } else if (result.content) {
            content = result.content;
        } else if (result.result) {
            content = typeof result.result === 'string' ? result.result : JSON.stringify(result.result, null, 2);
        } else {
            content = JSON.stringify(result, null, 2);
        }
        
        const formattedContent = this.formatAnalysisContent(content);
        
        resultsContainer.innerHTML = `
            <div class="analysis-header">
                <h4>${analysisType}</h4>
                <div class="analysis-meta">
                    <span class="timestamp">📅 ${date.toLocaleString()}</span>
                    <span class="session">🔗 Session: ${this.controller.sessionId.slice(-8)}</span>
                </div>
            </div>
            <div class="analysis-content">
                ${formattedContent}
            </div>
            ${result.tool_calls && result.tool_calls.length > 0 ? this.formatToolCalls(result.tool_calls) : ''}
        `;
        
        this.controller.uiManager.showSection('resultsSection');
    }

    /**
     * Format analysis content for display
     */
    formatAnalysisContent(content, summary = false) {
        if (typeof content === 'string') {
            return summary ? content.substring(0, 100) + '...' : content;
        } else if (typeof content === 'object' && content !== null) {
            // More robust handling for object content
            let formatted = '';
            if (content.content) {
                formatted += content.content;
            }
            if (content.tool_calls) {
                formatted += `\n\nTool Calls:\n${this.formatToolCalls(content.tool_calls)}`;
            }
            return summary ? formatted.substring(0, 100) + '...' : formatted;
        }
        return summary ? 'No content preview'.substring(0, 100) + '...' : 'No content';
    }

    /**
     * Format tool calls for display
     */
    formatToolCalls(toolCalls) {
        if (!toolCalls || toolCalls.length === 0) {
            return '';
        }
        let markdown = '\n\n**Tool Calls:**\n';
        toolCalls.forEach(call => {
            markdown += `* Tool: ${call.tool_name}\n`;
            markdown += `  * Parameters: ${JSON.stringify(call.parameters, null, 2)}\n`;
        });
        return markdown;
    }

    /**
     * Convert analysis data to markdown format
     */
    convertToMarkdown(analysisData) {
        if (!analysisData) {
            return '';
        }

        const { type, date, result } = analysisData;

        let markdown = `# ${type}\n\n`;
        markdown += `**Date:** ${new Date(date).toLocaleString()}\n\n`;

        if (typeof result === 'string') {
            markdown += `## Analysis Result\n\n${result}\n`;
        } else if (typeof result === 'object' && result !== null) {
            markdown += `## Analysis Result\n\n`;
            if (result.content) {
                markdown += `${result.content}\n\n`;
            }

            if (result.tool_calls) {
                markdown += this.formatToolCalls(result.tool_calls);
            }

            const otherData = { ...result };
            delete otherData.content;
            delete otherData.tool_calls;

            if (Object.keys(otherData).length > 0) {
                markdown += `### Additional Data\n\n`;
                markdown += `\`\`\`json\n${JSON.stringify(otherData, null, 2)}\n\`\`\`\n`;
            }
        } else {
            markdown += `## Analysis Result\n\n\`\`\`json\n${JSON.stringify(result, null, 2)}\n\`\`\`\n`;
        }

        return markdown;
    }

    /**
     * Copy analysis results to clipboard
     */
    async copyResults() {
        if (!this.controller.currentAnalysis || !this.controller.currentAnalysis.result) {
            this.controller.uiManager.showError('No results to copy');
            return;
        }
        
        try {
            const textToCopy = `${this.controller.currentAnalysis.type}\n${'='.repeat(50)}\n\n${this.controller.currentAnalysis.result.content || JSON.stringify(this.controller.currentAnalysis.result, null, 2)}`;
            await navigator.clipboard.writeText(textToCopy);
            this.controller.uiManager.showSuccess('Results copied to clipboard!');
        } catch (error) {
            console.error('Copy failed:', error);
            this.controller.uiManager.showError('Failed to copy results');
        }
    }

    /**
     * Export analysis results as markdown file
     */
    exportResults() {
        if (!this.controller.currentAnalysis) {
            this.controller.uiManager.showError('No analysis to export');
            return;
        }

        try {
            const exportData = {
                type: this.controller.currentAnalysis.type,
                date: this.controller.currentAnalysis.timestamp,
                result: this.controller.currentAnalysis.result
            };

            const markdownContent = this.convertToMarkdown(exportData);
            const blob = new Blob([markdownContent], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agent-hustle-analysis-${Date.now()}.md`;
            
            document.body.appendChild(a);
            a.click();
            
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.controller.uiManager.showSuccess('Analysis exported successfully!');
        } catch (error) {
            console.error('Error exporting results:', error);
            this.controller.uiManager.showError('Failed to export analysis');
        }
    }
}
