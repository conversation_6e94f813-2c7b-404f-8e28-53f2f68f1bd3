// Discord Webhook Integration Module
import { DISCORD_CONFIG } from '../../config.js';

/**
 * Send analysis results to Discord via webhook
 * @param {Object} analysisData - The analysis data to send
 * @param {string} webhookUrl - Discord webhook URL
 * @returns {Promise<Object>} - Result of the send operation
 */
export async function sendAnalysisToDiscord(analysisData, webhookUrl) {
    try {
        if (!webhookUrl) {
            throw new Error('Webhook URL is required');
        }

        const embed = formatAnalysisAsDiscordEmbed(analysisData);
        
        const result = await sendDiscordWebhook(webhookUrl, embed);
        
        return {
            success: true,
            messageId: result.messageId,
            result: result
        };
        
    } catch (error) {
        console.error('Error sending to Discord:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Send a message to Discord using webhook
 * @param {string} webhookUrl - Webhook URL
 * @param {Object} embed - Discord embed object
 * @returns {Promise<Object>} - API response
 */
async function sendDiscordWebhook(webhookUrl, embeds) {
    // Handle both single embed and array of embeds
    const embedArray = Array.isArray(embeds) ? embeds : [embeds];
    
    const payload = {
        embeds: embedArray,
        username: 'Agent Hustle Pro'
    };
    
    let lastError;
    
    for (let attempt = 1; attempt <= DISCORD_CONFIG.RETRY_ATTEMPTS; attempt++) {
        try {
            const response = await fetch(webhookUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload),
                signal: AbortSignal.timeout(DISCORD_CONFIG.TIMEOUT)
            });
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            
            return {
                success: true,
                messageId: 'sent',
                attempt: attempt
            };
            
        } catch (error) {
            lastError = error;
            console.warn(`Discord send attempt ${attempt} failed:`, error.message);
            
            if (attempt < DISCORD_CONFIG.RETRY_ATTEMPTS) {
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }
    
    throw lastError;
}

/**
 * Format analysis data as Discord embed
 * @param {Object} analysisData - Analysis data object
 * @returns {Array} - Array of Discord embed objects
 */
export function formatAnalysisAsDiscordEmbed(analysisData) {
    const { analysisType, result, date } = analysisData;
    
    let content = '';
    let summary = '';
    let keyPoints = [];
    
    if (typeof result === 'object' && result !== null) {
        content = result.content || result.analysis || '';
        summary = result.summary || '';
        keyPoints = result.keyPoints || [];
    } else if (typeof result === 'string') {
        content = result;
    }
    
    // Ensure date is a proper Date object
    let dateObj;
    if (date instanceof Date) {
        dateObj = date;
    } else if (typeof date === 'string') {
        dateObj = new Date(date);
    } else {
        dateObj = new Date();
    }

    const embeds = [];
    
    // Main embed with header info
    const mainEmbed = {
        title: '🤖 Agent Hustle Analysis Report',
        color: 0x5BA9F9,
        timestamp: dateObj.toISOString(),
        footer: {
            text: 'Powered by Agent Hustle Pro'
        },
        fields: []
    };
    
    mainEmbed.fields.push({
        name: '📊 Analysis Type',
        value: analysisType || 'General Analysis',
        inline: true
    });
    
    mainEmbed.fields.push({
        name: '📅 Generated',
        value: formatDate(dateObj),
        inline: true
    });
    
    // Add summary if available
    if (summary && summary.trim()) {
        const cleanSummary = cleanContentForDiscord(summary.trim());
        if (cleanSummary.length <= 1024) {
            mainEmbed.fields.push({
                name: '📋 Executive Summary',
                value: cleanSummary,
                inline: false
            });
        } else {
            // Split summary into multiple fields if too long
            const summaryChunks = splitTextIntoChunks(cleanSummary, 1020);
            summaryChunks.forEach((chunk, index) => {
                mainEmbed.fields.push({
                    name: index === 0 ? '📋 Executive Summary' : '📋 Summary (continued)',
                    value: chunk,
                    inline: false
                });
            });
        }
    }
    
    embeds.push(mainEmbed);
    
    // Handle detailed content - use separate embed if needed
    if (content && content.trim()) {
        const cleanContent = cleanContentForDiscord(content.trim());
        
        if (cleanContent.length <= 1024) {
            // Add to main embed if it fits
            mainEmbed.fields.push({
                name: '📝 Detailed Analysis',
                value: cleanContent,
                inline: false
            });
        } else {
            // Create separate embed for detailed content
            const contentEmbed = {
                color: 0x5BA9F9,
                fields: []
            };
            
            const contentChunks = splitTextIntoChunks(cleanContent, 1020);
            contentChunks.forEach((chunk, index) => {
                contentEmbed.fields.push({
                    name: index === 0 ? '📝 Detailed Analysis' : '📝 Analysis (continued)',
                    value: chunk,
                    inline: false
                });
            });
            
            embeds.push(contentEmbed);
        }
    }
    
    // Add key points if available
    if (keyPoints && Array.isArray(keyPoints) && keyPoints.length > 0) {
        const keyPointsText = keyPoints
            .slice(0, 10) // Increased from 5 to 10
            .map((point, index) => `${index + 1}. ${cleanContentForDiscord(point.trim())}`)
            .join('\n');
        
        if (keyPointsText.length <= 1024) {
            // Try to add to existing embed
            const targetEmbed = embeds[embeds.length - 1];
            if (targetEmbed.fields.length < 25) {
                targetEmbed.fields.push({
                    name: '🔑 Key Insights',
                    value: keyPointsText,
                    inline: false
                });
            } else {
                // Create new embed for key points
                embeds.push({
                    color: 0x5BA9F9,
                    fields: [{
                        name: '🔑 Key Insights',
                        value: keyPointsText,
                        inline: false
                    }]
                });
            }
        } else {
            // Split key points across multiple fields
            const keyPointChunks = splitTextIntoChunks(keyPointsText, 1020);
            keyPointChunks.forEach((chunk, index) => {
                const targetEmbed = embeds[embeds.length - 1];
                if (targetEmbed.fields.length < 25) {
                    targetEmbed.fields.push({
                        name: index === 0 ? '🔑 Key Insights' : '🔑 Insights (continued)',
                        value: chunk,
                        inline: false
                    });
                } else {
                    embeds.push({
                        color: 0x5BA9F9,
                        fields: [{
                            name: index === 0 ? '🔑 Key Insights' : '🔑 Insights (continued)',
                            value: chunk,
                            inline: false
                        }]
                    });
                }
            });
        }
    }
    
    return embeds;
}

function splitTextIntoChunks(text, maxLength) {
    if (!text || text.length <= maxLength) {
        return [text];
    }
    
    const chunks = [];
    let currentChunk = '';
    const sentences = text.split(/(?<=[.!?])\s+/);
    
    for (const sentence of sentences) {
        if ((currentChunk + sentence).length <= maxLength) {
            currentChunk += (currentChunk ? ' ' : '') + sentence;
        } else {
            if (currentChunk) {
                chunks.push(currentChunk);
                currentChunk = sentence;
            } else {
                // Single sentence is too long, split by words
                const words = sentence.split(' ');
                let wordChunk = '';
                for (const word of words) {
                    if ((wordChunk + word).length <= maxLength) {
                        wordChunk += (wordChunk ? ' ' : '') + word;
                    } else {
                        if (wordChunk) {
                            chunks.push(wordChunk);
                            wordChunk = word;
                        } else {
                            // Single word is too long, truncate
                            chunks.push(word.substring(0, maxLength - 3) + '...');
                        }
                    }
                }
                if (wordChunk) {
                    currentChunk = wordChunk;
                }
            }
        }
    }
    
    if (currentChunk) {
        chunks.push(currentChunk);
    }
    
    return chunks.length > 0 ? chunks : [text.substring(0, maxLength)];
}

function cleanContentForDiscord(text) {
    if (!text || typeof text !== 'string') {
        return '';
    }
    
    return text
        .replace(/\\/g, '\\\\')
        .replace(/\*/g, '\\*')
        .replace(/_/g, '\\_')
        .replace(/~/g, '\\~')
        .replace(/`/g, '\\`')
        .replace(/\|/g, '\\|');
}

export async function testDiscordWebhook(webhookUrl) {
    try {
        if (!webhookUrl) {
            throw new Error('Webhook URL is required');
        }
        
        if (!validateWebhookUrl(webhookUrl)) {
            throw new Error('Invalid webhook URL format');
        }
        
        const testEmbed = {
            title: '🔧 Test Message',
            description: 'Discord webhook integration is working correctly!',
            color: 0x00FF00,
            timestamp: new Date().toISOString(),
            footer: {
                text: 'Agent Hustle Pro Analyzer'
            },
            fields: [
                {
                    name: '⏰ Time',
                    value: formatDate(new Date()),
                    inline: true
                },
                {
                    name: '✅ Status',
                    value: 'Connection Successful',
                    inline: true
                }
            ]
        };
        
        const payload = {
            embeds: [testEmbed],
            username: 'Agent Hustle Pro',
            content: '🚀 **Discord Integration Test**'
        };
        
        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload),
            signal: AbortSignal.timeout(DISCORD_CONFIG.TIMEOUT)
        });
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
        
        return {
            success: true,
            message: 'Discord webhook test successful!'
        };
        
    } catch (error) {
        console.error('Discord webhook test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

export function validateWebhookUrl(url) {
    if (!url || typeof url !== 'string') {
        return false;
    }
    
    const webhookRegex = /^https:\/\/discord\.com\/api\/webhooks\/\d+\/[\w-]+$/;
    return webhookRegex.test(url.trim());
}

function formatDate(date) {
    if (!date || !(date instanceof Date)) {
        date = new Date();
    }
    
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
    });
} 