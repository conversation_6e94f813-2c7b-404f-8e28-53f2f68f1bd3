---
description: 
globs: 
alwaysApply: false
---
# 🔧 Enhanced Refactoring Rules & Pre-Analysis Tools

## 🎯 **Core Principles**
1. **Never Break Existing Functionality** - Backward compatibility first
2. **Incremental Changes** - Small, testable changes over massive rewrites  
3. **Test Immediately** - Validate after each change, not at the end
4. **Rollback Ready** - Always have a way to revert quickly

## 🔍 **MANDATORY: Pre-Refactoring Analysis Tools**

### **Phase 0: Automated Code Analysis (REQUIRED)**
Run these tools **BEFORE** touching any code:

```bash
# 1. Code Complexity Analysis
python complexity_analyzer.py target-file.js

# 2. Dependency Mapping & Visualization 
python dependency_mapper.py js/
python dependency_visualizer.py js/

# 3. Code Smell Detection
python code_smell_detector.py target-file.js

# 4. Complete Method Inventory
python method_inventory.py target-file.js
```

### **Tool 1: Code Complexity Analyzer**
**Purpose:** Calculate cyclomatic complexity to identify functions that need splitting
**Output:** Complexity scores per function, refactoring priority list
**Threshold:** Functions >10 complexity = immediate refactor candidates

### **Tool 2: Dependency Mapper & Visualizer**
**dependency_mapper.py:** Uses esprima to parse JS files and map import/export dependencies
**dependency_visualizer.py:** Generates visual dependency graphs using AST parsing
**Output:** JSON dependency report + SVG dependency graph + circular dependency warnings
**Critical:** Identifies hidden dependencies that could break during refactoring

### **Tool 3: Code Smell Detector**
**Purpose:** Automated detection of anti-patterns using ESLint + SonarJS patterns
**Detects:** Long functions, duplicated code, too many parameters, deep nesting
**Output:** Prioritized list of code smells with severity ratings

### **Tool 4: Method Inventory Generator**
**Purpose:** Complete catalog of all methods, classes, and their signatures
**Output:** JSON inventory for before/after comparison
**Critical:** Prevents "method is not a function" errors

## 📋 **Pre-Refactoring Checklist**

### **Analysis Phase (30 minutes)**
- [ ] **Run all 4 analysis tools** - Get complete picture before starting
- [ ] **Create backup** - Full working code backup
- [ ] **Document current functionality** - Screenshot key features
- [ ] **Map user journeys** - Complete feature flows end-to-end
- [ ] **Identify high-complexity functions** - Focus refactoring efforts
- [ ] **Check for circular dependencies** - Resolve before refactoring
- [ ] **Catalog code smells** - Prioritize fixes

### **Planning Phase (15 minutes)**
- [ ] **Set measurable goals** - Define success criteria
- [ ] **Plan module structure** - Design target architecture
- [ ] **Assign methods to modules** - Based on complexity analysis
- [ ] **Plan testing strategy** - How to validate each change

## 🏗️ **Modular Architecture Rules**

### **File Size Limits (STRICT)**
- **500 lines max per file** - Split immediately if exceeded
- **100 lines max per function** - Break into smaller functions
- **250 lines max per class** - Extract methods or split responsibilities

### **Module Organization**
```
/feature-name/
├── featureManager.js      # Coordination (core methods)
├── featureUI.js          # User interface
├── featureAPI.js         # External interactions
├── featureUtils.js       # Helper functions
└── index.js              # Public exports
```

### **Method Classification & Migration**
1. **Core Methods** → Stay in main file (initialization, coordination)
2. **Feature Methods** → Move to feature-specific modules
3. **UI Methods** → Move to UI modules  
4. **Utility Methods** → Move to utils modules
5. **API Methods** → Move to API modules

## 🔄 **Import/Export Management**

### **Import Preservation Rules**
- **Never Remove Working Imports** - If an import works, keep it during refactoring
- **Add Imports Incrementally** - Add new imports as needed for new modules
- **Use Consistent Import Style** - Maintain existing import patterns
- **Document Import Changes** - Track what imports were added/modified

### **Export Strategies**
```javascript
// ✅ Good - Named exports for multiple functions
export { functionA, functionB, functionC };

// ✅ Good - Default export for single-purpose modules
export default class MainComponent { }

// ✅ Good - Index file for clean imports
// index.js
export { ModuleA } from './moduleA.js';
export { ModuleB } from './moduleB.js';
```

### **Dependency Injection Pattern**
```javascript
// ✅ Good - Dependencies passed in constructor
class FeatureManager {
    constructor(uiManager, apiManager, storageManager) {
        this.ui = uiManager;
        this.api = apiManager;
        this.storage = storageManager;
    }
}

// ❌ Bad - Hard-coded dependencies
class FeatureManager {
    constructor() {
        this.ui = new UIManager(); // Hard to test/mock
    }
}
```

## 🔧 **Refactoring Process (Systematic)**

### **Phase 1: Module Creation (Based on Analysis)**
1. **Create module templates** - Set up files with method stubs
2. **Implement high-complexity functions first** - Tackle hardest parts
3. **Move complete features** - Never move partial functionality
4. **Preserve method signatures** - Exact same names and parameters
5. **Test each module individually** - Verify isolation

### **Phase 2: Integration & Delegation**
1. **Update main file delegation** - Route calls to modules
2. **Verify all method calls** - Use method inventory to check
3. **Test module communication** - Ensure data flows work
4. **Fix dependencies** - Resolve any import issues

### **Phase 3: CRITICAL Validation Protocol**
**Execute IMMEDIATELY after any change:**

#### **Quick Validation (2 minutes)**
```bash
# Syntax check
node -c refactored-file.js

# Method existence check
python3 method_validator.py original.js refactored.js modules/
```

#### **Feature Flow Testing (10 minutes)**
- [ ] **Open browser console** - Check for errors
- [ ] **Test critical user journeys** - Key features work
- [ ] **Verify UI interactions** - All buttons/forms respond
- [ ] **Check performance** - No significant slowdown

#### **Rollback Triggers (STOP immediately if ANY occur)**
- ❌ Console errors: "X is not a function"
- ❌ Features not working that worked before
- ❌ Performance >2x slower
- ❌ UI elements not responding

## 🚨 **Critical Refactoring Pitfalls & Real-World Lessons**

### **🔥 CRITICAL: Complete Functionality Preservation Protocol**
**Real Example:** HustlePlug refactoring broke Pro settings, analysis functions, and prompt management
**Root Cause:** Incomplete method migration and missing integration validation

**MANDATORY Pre-Refactoring Checklist:**
- [ ] **Create comprehensive method inventory** - List ALL methods in original file
- [ ] **Map complete user journeys** - Document end-to-end feature flows
- [ ] **Identify integration points** - Where modules must communicate
- [ ] **Document UI/UX requirements** - Preserve original user experience
- [ ] **Create functionality test matrix** - Test scenarios for each feature

### **🔥 CRITICAL: Method Name & API Mismatches**
**Real Example:** `this.promptPagination.updatePaginationUI is not a function`

**Root Cause:** Assuming method names without verifying actual API
**Prevention Rules:**
- [ ] **Always verify method names** before calling them in refactored code
- [ ] **Check actual class/module APIs** - don't assume method names
- [ ] **Use IDE autocomplete** to verify available methods
- [ ] **Test method calls immediately** after moving code

```javascript
// ❌ DANGEROUS - Assuming method exists
this.pagination.updatePaginationUI(); // Might not exist!

// ✅ SAFE - Verify method exists first
if (typeof this.pagination.updatePaginationUI === 'function') {
    this.pagination.updatePaginationUI();
} else {
    console.error('Method updatePaginationUI not found, checking available methods:', 
                  Object.getOwnPropertyNames(this.pagination));
}
```

### **🔥 CRITICAL: Incomplete Feature Migration**
**Real Example:** Discord/Telegram settings not loading despite Pro key

**Root Cause:** Moving UI code without moving the initialization logic
**Prevention Rules:**
- [ ] **Map complete feature flows** - from initialization to UI display
- [ ] **Trace data flow end-to-end** - ensure all steps are preserved
- [ ] **Test feature activation conditions** - verify triggers still work
- [ ] **Check conditional logic** - ensure all if/when conditions are met

```javascript
// ❌ DANGEROUS - Moving only part of feature
// Moved UI rendering but forgot initialization
showTelegramSettings() {
    // This won't work if setupTelegramSettings() wasn't called
    document.getElementById('telegram-panel').style.display = 'block';
}

// ✅ SAFE - Complete feature migration
async initializeTelegramFeature() {
    await this.setupTelegramSettings();
    this.bindTelegramEventListeners();
    this.loadTelegramConfig();
}

showTelegramSettings() {
    if (!this.telegramInitialized) {
        console.warn('Telegram not initialized, initializing now...');
        this.initializeTelegramFeature();
    }
    document.getElementById('telegram-panel').style.display = 'block';
}
```

### **🔥 CRITICAL: Performance Degradation from Refactoring**
**Real Example:** Key validation taking too long after refactoring

**Root Cause:** Adding unnecessary async/await chains or redundant operations
**Prevention Rules:**
- [ ] **Profile before and after** - measure performance impact
- [ ] **Avoid unnecessary async chains** - don't add await where not needed
- [ ] **Check for redundant operations** - ensure no duplicate processing
- [ ] **Monitor critical path performance** - especially user-facing operations

```javascript
// ❌ PERFORMANCE KILLER - Unnecessary async chains
async validateKey() {
    await this.ui.showLoading();           // Unnecessary await
    await this.storage.prepare();          // Might not need await
    const result = await this.api.validate(); // Only this needs await
    await this.ui.hideLoading();           // Unnecessary await
    return result;
}

// ✅ PERFORMANCE OPTIMIZED - Only await what's necessary
async validateKey() {
    this.ui.showLoading();                 // Synchronous UI update
    this.storage.prepare();                // If synchronous, don't await
    const result = await this.api.validate(); // Only await async operations
    this.ui.hideLoading();                 // Synchronous UI update
    return result;
}
```

### **🔥 CRITICAL: Event Listener & Initialization Order**
**Real Example:** Settings panels not responding to clicks

**Root Cause:** Moving event listener setup without ensuring proper initialization order
**Prevention Rules:**
- [ ] **Map initialization dependencies** - what must happen before what
- [ ] **Preserve event listener timing** - ensure DOM elements exist before binding
- [ ] **Test user interactions immediately** - click buttons, test forms
- [ ] **Check console for event errors** - missing elements, failed bindings

```javascript
// ❌ DANGEROUS - Event listeners before DOM ready
class SettingsManager {
    constructor() {
        this.setupEventListeners(); // DOM might not be ready!
    }
    
    setupEventListeners() {
        document.getElementById('telegram-btn').addEventListener('click', ...); // Might fail!
    }
}

// ✅ SAFE - Proper initialization order
class SettingsManager {
    constructor() {
        // Don't setup listeners in constructor
    }
    
    async initialize() {
        await this.waitForDOM();
        this.setupEventListeners();
        this.loadSettings();
    }
    
    waitForDOM() {
        return new Promise(resolve => {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', resolve);
            } else {
                resolve();
            }
        });
    }
}
```

## 🛠️ **Enhanced Analysis Tools Implementation**

### **Tool Integration Workflow**
```bash
# Complete Pre-Refactoring Analysis (45 minutes)
python3 complexity_analyzer.py popup.js > complexity-report.json
python3 dependency_mapper.py js/ # Creates dependency_report.json
python3 dependency_visualizer.py js/ > dependency-visual.json  
python3 code_smell_detector.py popup.js > smells-report.json
python3 method_inventory.py popup.js > methods-inventory.json

# Review reports and plan refactoring
# Create backup
cp popup.js popup-original-backup.js

# Start refactoring with data-driven decisions
```

### **Post-Refactoring Validation (15 minutes)**
```bash
# Re-run analysis tools
python3 complexity_analyzer.py popup.js > complexity-after.json
python3 method_inventory.py popup.js > methods-after.json

# Compare before/after
python3 compare_analysis.py complexity-report.json complexity-after.json
python3 validate_methods.py methods-inventory.json methods-after.json

# Manual testing
# - Open browser console
# - Test all critical features
# - Verify performance
```

### **Real-Time Method Existence Checker**
```javascript
// Add this to your refactored code during development
function verifyMethodExists(obj, methodName, context = 'Unknown') {
    if (typeof obj[methodName] !== 'function') {
        console.error(`❌ METHOD MISSING: ${context}.${methodName} is not a function`);
        console.log('Available methods:', Object.getOwnPropertyNames(obj).filter(name => 
            typeof obj[name] === 'function'));
        return false;
    }
    console.log(`✅ METHOD VERIFIED: ${context}.${methodName} exists`);
    return true;
}

// Usage in refactored code:
if (verifyMethodExists(this.pagination, 'updatePaginationUI', 'pagination')) {
    this.pagination.updatePaginationUI();
}
```

### **API Compatibility Validator**
```javascript
// Validate that refactored modules maintain expected APIs
class APIValidator {
    static validateModule(module, expectedMethods, moduleName) {
        const missing = [];
        const available = Object.getOwnPropertyNames(module).filter(name => 
            typeof module[name] === 'function');
        
        expectedMethods.forEach(method => {
            if (!available.includes(method)) {
                missing.push(method);
            }
        });
        
        if (missing.length > 0) {
            console.error(`❌ ${moduleName} missing methods:`, missing);
            console.log(`Available methods:`, available);
            return false;
        }
        
        console.log(`✅ ${moduleName} API validation passed`);
        return true;
    }
    
    static validateAllModules(modules) {
        let allValid = true;
        for (const [moduleName, {instance, expectedMethods}] of Object.entries(modules)) {
            if (!this.validateModule(instance, expectedMethods, moduleName)) {
                allValid = false;
            }
        }
        return allValid;
    }
}

// Usage:
const moduleValidation = {
    'UIManager': {
        instance: this.uiManager,
        expectedMethods: ['showSection', 'showError', 'showSuccess', 'updateUI', 'displayResults']
    },
    'AnalysisManager': {
        instance: this.analysisManager,
        expectedMethods: ['analyzeSelection', 'analyzePage', 'runCustomAnalysis', 'performAnalysis']
    },
    'SettingsManager': {
        instance: this.settingsManager,
        expectedMethods: ['loadSettingsContent', 'loadMembershipInfo', 'loadTelegramSettings']
    }
};

APIValidator.validateAllModules(moduleValidation);
```

## 🧪 **Testing & Validation Rules**

### **🔍 CRITICAL: Post-Refactoring Validation Protocol**
**Based on Real-World Failures - Execute IMMEDIATELY after refactoring**

#### **Phase 0: Pre-Validation Setup (2 minutes)**
```bash
# Create method inventory comparison
python3 refactoring_analysis.py original-file.js refactored-file.js
# Generate missing functionality report
```

#### **Phase 1: Method Call Validation (5 minutes)**
```bash
# Check for "is not a function" errors
grep -r "\..*(" refactored-files/ | head -20
# Manually verify each method call exists in target modules
```

**Manual Checklist:**
- [ ] Open browser console and interact with ALL UI elements
- [ ] Click every button, open every modal, test every feature
- [ ] Look for red console errors immediately
- [ ] Test pagination, settings, integrations specifically

#### **Phase 2: Complete Feature Flow Testing (15 minutes)**
**Test Complete User Journeys:**
- [ ] **Pro Key Validation** - Enter key, verify all pro features unlock
- [ ] **Settings Access** - Verify Discord/Telegram settings appear with pro key
- [ ] **Analysis Features** - Test analysis generation and history
- [ ] **Integration Features** - Test sending to Discord/Telegram
- [ ] **UI Interactions** - Test all buttons, modals, forms
- [ ] **Search/Filter Functions** - Test all search and filtering capabilities
- [ ] **Import/Export Features** - Test data import/export functionality
- [ ] **Context Menu Integration** - Test right-click menu features

#### **Phase 3: UI/UX Preservation Validation (10 minutes)**
- [ ] **Visual Design Consistency** - Compare UI with original screenshots
- [ ] **Card-based Layouts** - Verify card designs match original
- [ ] **Search/Filter UI** - Ensure search boxes and filters are present
- [ ] **Pagination UI** - Check pagination controls and styling
- [ ] **Modal Dialogs** - Test all modal windows and overlays
- [ ] **Notification Systems** - Verify error/success messages work

#### **Phase 4: Performance Validation (5 minutes)**
- [ ] **Time critical operations** - Key validation should be <2 seconds
- [ ] **Check for unnecessary async/await** - Remove performance killers
- [ ] **Monitor memory usage** - Ensure no memory leaks from refactoring
- [ ] **Test on slow connections** - Verify performance under load

#### **Phase 5: Integration Testing (10 minutes)**
```javascript
// Add temporary debug logging to catch issues
console.log('🔍 DEBUG: Method exists?', typeof this.pagination.updatePaginationUI);
console.log('🔍 DEBUG: Available methods:', Object.getOwnPropertyNames(this.pagination));
console.log('🔍 DEBUG: Pro status:', this.proStatus);
console.log('🔍 DEBUG: Settings initialized:', this.settingsInitialized);
console.log('🔍 DEBUG: All modules loaded:', {
    ui: !!this.uiManager,
    analysis: !!this.analysisManager,
    settings: !!this.settingsManager,
    history: !!this.historyManager,
    integration: !!this.integrationManager
});
```

**Critical Test Scenarios:**
- [ ] **Fresh browser session** - Test without cached data
- [ ] **Different pro key states** - Test with/without pro key
- [ ] **Error conditions** - Test with invalid inputs
- [ ] **Edge cases** - Test boundary conditions
- [ ] **Module Communication** - Verify modules can communicate properly

#### **Phase 6: Functionality Completeness Check (10 minutes)**
**Use the generated missing functionality report to verify:**
- [ ] **All original methods implemented** - No missing core functions
- [ ] **Event listeners working** - All buttons and interactions respond
- [ ] **Settings integration complete** - Pro features accessible
- [ ] **Search/filter functionality** - All search capabilities present
- [ ] **Import/export working** - Data management features functional

#### **Immediate Rollback Triggers**
**If ANY of these occur, STOP and rollback immediately:**
- ❌ Console errors: "X is not a function"
- ❌ Features not loading that worked before
- ❌ Performance >2x slower than before
- ❌ UI elements not responding to clicks
- ❌ Data not saving/loading properly
- ❌ Pro features inaccessible with valid key
- ❌ Search/filter functionality missing
- ❌ UI design significantly different from original

#### **Success Criteria Checklist**
**Only proceed if ALL are true:**
- ✅ Zero new console errors
- ✅ All features work as before
- ✅ Performance maintained or improved
- ✅ All UI interactions responsive
- ✅ Complete user journeys successful
- ✅ Pro features fully accessible
- ✅ Search/filter functionality preserved
- ✅ UI/UX matches original design
- ✅ All import/export features working
- ✅ Module integration seamless

## 🚨 **Critical Success Metrics**

### **Mandatory Validation Checklist**
- [ ] **Zero new console errors** - Most critical success metric
- [ ] **All features work as before** - Complete functionality preservation
- [ ] **Performance maintained** - No significant slowdown
- [ ] **Code complexity reduced** - Measurable improvement in complexity scores
- [ ] **Dependencies clean** - No circular dependencies introduced

### **Rollback Immediately If:**
- Any "is not a function" errors appear
- Core features stop working
- Performance degrades >50%
- New circular dependencies detected
- Complexity scores increase instead of decrease

## 🔄 **Incremental Refactoring Process**

### **Phase 1: Comprehensive Preparation**
1. **Create Backup** - Full backup of working code
2. **Generate Method Inventory** - Use analysis tools to list ALL methods
3. **Map User Journeys** - Document complete feature flows end-to-end
4. **Document UI/UX Requirements** - Screenshot and document current design
5. **Create Integration Map** - Identify how components communicate
6. **Set Up Testing** - Ensure robust testing infrastructure
7. **Plan Modules** - Design the target modular structure with method assignments

### **Phase 2: Systematic Module Creation**
1. **Create Module Templates** - Set up module files with method stubs
2. **Implement Core Methods First** - Start with most critical functionality
3. **Move Complete Features** - Move entire feature sets, not partial functionality
4. **Preserve Method Signatures** - Keep exact same method names and parameters
5. **Update Imports Incrementally** - Adjust import statements as you go
6. **Test Each Module Individually** - Verify each module works in isolation

### **Phase 3: Integration & Delegation**
1. **Update Main File Delegation** - Modify main file to use new modules
2. **Verify Method Calls** - Ensure all delegated methods exist in target modules
3. **Test Module Communication** - Ensure modules can communicate properly
4. **Fix Dependencies** - Resolve any dependency issues
5. **Validate Event Handling** - Ensure all event listeners work correctly
6. **Test Integration Points** - Verify data flows between modules

### **Phase 4: Comprehensive Validation**
1. **Execute Full Validation Protocol** - Run all 6 phases of validation
2. **Compare with Original** - Use analysis tools to verify completeness
3. **Test All User Journeys** - Verify every feature works end-to-end
4. **Performance Validation** - Ensure no performance degradation
5. **UI/UX Verification** - Confirm design and experience preserved
6. **Generate Completeness Report** - Document what was preserved/restored

### **Phase 5: Optimization & Documentation**
1. **Remove Dead Code** - Clean up unused code (only after validation)
2. **Optimize Imports** - Clean up import statements
3. **Performance Tuning** - Optimize for better performance
4. **Update Documentation** - Update all documentation
5. **Create Migration Guide** - Document changes for team
6. **Establish Monitoring** - Set up ongoing validation checks

## 📊 **Code Quality Metrics**

### **Complexity Reduction**
- **Cyclomatic Complexity** - Reduce complex conditional logic
- **Nesting Depth** - Limit nesting to 3-4 levels maximum
- **Function Parameters** - Limit to 3-5 parameters per function
- **Code Duplication** - Eliminate repeated code blocks

### **Maintainability Improvements**
- **Clear Naming** - Use descriptive, self-documenting names
- **Consistent Patterns** - Follow established patterns throughout codebase
- **Documentation** - Add comments for complex logic
- **Type Safety** - Use TypeScript or JSDoc for better type checking

## 🏆 **Success Definition**
**Refactoring is successful ONLY when:**
1. ✅ **Complexity reduced** - Lower cyclomatic complexity scores
2. ✅ **Zero functionality lost** - All features work identically  
3. ✅ **Dependencies clean** - No circular dependencies
4. ✅ **Code smells reduced** - Fewer anti-patterns detected
5. ✅ **Performance maintained** - Same or better speed
6. ✅ **Team confidence high** - Easy to understand and modify

## 🎓 **Lessons Learned from Real-World Refactoring Failures**

### **The HustlePlug Chrome Extension Case Study**
**Project:** 3,308-line popup.js refactored into 5 modular components
**Success:** 69% code reduction, improved maintainability
**Failures:** 4 critical runtime errors that could have been prevented

### **💡 Golden Rules from Real Experience**

1. **"If it works, verify before you move it"** - Don't assume APIs
2. **"Move features completely, not partially"** - Include initialization
3. **"Test immediately, not later"** - Catch errors in minutes, not days
4. **"Performance first, elegance second"** - Don't sacrifice speed for beauty
5. **"When in doubt, add debug logging"** - Temporary logging saves hours

### **⚡ 30-Second Validation Rule**
**After ANY refactoring change:**
1. Open browser console (5 seconds)
2. Click the main feature buttons (10 seconds)  
3. Look for red errors (5 seconds)
4. Test one complete user flow (10 seconds)

**If ANY errors appear, stop and fix immediately. Don't accumulate technical debt.**

### **🏆 Success Metrics That Matter**
- **Zero new console errors** (most important)
- **All features work as before** (user experience)
- **Performance maintained** (user satisfaction)
- **Code is more maintainable** (developer experience)


**Remember: The best refactoring is invisible to users but obvious to developers.**