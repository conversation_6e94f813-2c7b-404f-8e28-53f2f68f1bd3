// Performance Test Script for HustlePlug Pro Validation API
// Tests response times, throughput, and concurrent requests
// Run with: node test-api-performance.js

const API_URL = 'https://hustleplug-pro-validation-mswm0dc9o-calel33s-projects.vercel.app/api/validate-key';

async function measureResponseTime(key, testName) {
    const startTime = Date.now();
    
    try {
        const response = await fetch(API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ key })
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        if (!response.ok) {
            return { success: false, responseTime, error: `HTTP ${response.status}` };
        }
        
        const result = await response.json();
        return { success: true, responseTime, result };
        
    } catch (error) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        return { success: false, responseTime, error: error.message };
    }
}

async function performanceTest() {
    console.log('⚡ HustlePlug Pro API Performance Test\n');
    console.log('=' * 50);
    
    // Test 1: Single Request Response Time
    console.log('\n📊 Test 1: Single Request Response Time');
    console.log('-' * 40);
    
    const singleTests = [
        { key: 'pro_demo_key_12345', name: 'Valid Pro Key' },
        { key: 'premium_demo_key_67890', name: 'Valid Premium Key' },
        { key: 'expired_demo_key_11111', name: 'Expired Key' },
        { key: 'invalid_key_123', name: 'Invalid Key' }
    ];
    
    const singleResults = [];
    
    for (const test of singleTests) {
        console.log(`\n🔍 Testing: ${test.name}`);
        const result = await measureResponseTime(test.key, test.name);
        singleResults.push({ ...test, ...result });
        
        if (result.success) {
            console.log(`✅ Response time: ${result.responseTime}ms`);
            console.log(`   Status: ${result.result.isPro ? 'Pro' : 'Not Pro'}`);
        } else {
            console.log(`❌ Failed: ${result.error} (${result.responseTime}ms)`);
        }
    }
    
    // Test 2: Multiple Sequential Requests
    console.log('\n\n📊 Test 2: Sequential Requests (10x)');
    console.log('-' * 40);
    
    const sequentialTimes = [];
    const testKey = 'pro_demo_key_12345';
    
    console.log(`🔄 Running 10 sequential requests...`);
    
    for (let i = 0; i < 10; i++) {
        const result = await measureResponseTime(testKey, `Sequential ${i + 1}`);
        sequentialTimes.push(result.responseTime);
        process.stdout.write(`${i + 1} `);
    }
    
    console.log('\n');
    
    const avgSequential = sequentialTimes.reduce((a, b) => a + b, 0) / sequentialTimes.length;
    const minSequential = Math.min(...sequentialTimes);
    const maxSequential = Math.max(...sequentialTimes);
    
    console.log(`📈 Sequential Results:`);
    console.log(`   Average: ${avgSequential.toFixed(2)}ms`);
    console.log(`   Min: ${minSequential}ms`);
    console.log(`   Max: ${maxSequential}ms`);
    
    // Test 3: Concurrent Requests
    console.log('\n\n📊 Test 3: Concurrent Requests (5x)');
    console.log('-' * 40);
    
    console.log(`🚀 Running 5 concurrent requests...`);
    
    const concurrentPromises = [];
    for (let i = 0; i < 5; i++) {
        concurrentPromises.push(measureResponseTime(testKey, `Concurrent ${i + 1}`));
    }
    
    const concurrentResults = await Promise.all(concurrentPromises);
    const concurrentTimes = concurrentResults.map(r => r.responseTime);
    
    const avgConcurrent = concurrentTimes.reduce((a, b) => a + b, 0) / concurrentTimes.length;
    const minConcurrent = Math.min(...concurrentTimes);
    const maxConcurrent = Math.max(...concurrentTimes);
    
    console.log(`📈 Concurrent Results:`);
    console.log(`   Average: ${avgConcurrent.toFixed(2)}ms`);
    console.log(`   Min: ${minConcurrent}ms`);
    console.log(`   Max: ${maxConcurrent}ms`);
    console.log(`   Success Rate: ${concurrentResults.filter(r => r.success).length}/5`);
    
    // Test 4: Throughput Test
    console.log('\n\n📊 Test 4: Throughput Test (30 seconds)');
    console.log('-' * 40);
    
    console.log(`⏱️  Running requests for 30 seconds...`);
    
    const throughputStart = Date.now();
    const throughputEnd = throughputStart + 30000; // 30 seconds
    let requestCount = 0;
    let successCount = 0;
    const throughputTimes = [];
    
    while (Date.now() < throughputEnd) {
        const result = await measureResponseTime(testKey, `Throughput ${requestCount + 1}`);
        requestCount++;
        if (result.success) {
            successCount++;
            throughputTimes.push(result.responseTime);
        }
        
        // Show progress every 10 requests
        if (requestCount % 10 === 0) {
            process.stdout.write(`${requestCount} `);
        }
    }
    
    console.log('\n');
    
    const actualDuration = Date.now() - throughputStart;
    const requestsPerSecond = (requestCount / actualDuration) * 1000;
    const avgThroughputTime = throughputTimes.length > 0 
        ? throughputTimes.reduce((a, b) => a + b, 0) / throughputTimes.length 
        : 0;
    
    console.log(`📈 Throughput Results:`);
    console.log(`   Total Requests: ${requestCount}`);
    console.log(`   Successful: ${successCount}`);
    console.log(`   Success Rate: ${((successCount / requestCount) * 100).toFixed(1)}%`);
    console.log(`   Requests/Second: ${requestsPerSecond.toFixed(2)}`);
    console.log(`   Average Response Time: ${avgThroughputTime.toFixed(2)}ms`);
    
    // Summary
    console.log('\n\n🎯 PERFORMANCE SUMMARY');
    console.log('=' * 50);
    
    console.log(`\n📊 Response Time Analysis:`);
    console.log(`   Single Request Average: ${singleResults.reduce((sum, r) => sum + (r.responseTime || 0), 0) / singleResults.length}ms`);
    console.log(`   Sequential Average: ${avgSequential.toFixed(2)}ms`);
    console.log(`   Concurrent Average: ${avgConcurrent.toFixed(2)}ms`);
    console.log(`   Throughput Average: ${avgThroughputTime.toFixed(2)}ms`);
    
    console.log(`\n⚡ Performance Rating:`);
    const overallAvg = (avgSequential + avgConcurrent + avgThroughputTime) / 3;
    
    if (overallAvg < 200) {
        console.log(`   🟢 EXCELLENT (${overallAvg.toFixed(0)}ms avg) - API is very fast`);
    } else if (overallAvg < 500) {
        console.log(`   🟡 GOOD (${overallAvg.toFixed(0)}ms avg) - API performance is acceptable`);
    } else if (overallAvg < 1000) {
        console.log(`   🟠 FAIR (${overallAvg.toFixed(0)}ms avg) - API could be optimized`);
    } else {
        console.log(`   🔴 SLOW (${overallAvg.toFixed(0)}ms avg) - API needs optimization`);
    }
    
    console.log(`\n📝 Recommendations:`);
    if (overallAvg < 300) {
        console.log(`   ✅ API performance is excellent for production use`);
        console.log(`   ✅ Can handle real-time validation requests`);
        console.log(`   ✅ Suitable for high-frequency usage`);
    } else {
        console.log(`   ⚠️  Consider caching frequently accessed keys`);
        console.log(`   ⚠️  Monitor response times in production`);
        console.log(`   ⚠️  Consider implementing request batching`);
    }
}

// Run the performance test
performanceTest().catch(console.error); 