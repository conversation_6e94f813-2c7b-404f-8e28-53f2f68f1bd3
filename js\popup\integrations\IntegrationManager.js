/**
 * Integration Manager
 * Handles Telegram and Discord integration UI and manual sending
 */
import { BaseManager } from '../core/BaseManager.js';
import { sendAnalysisToTelegram } from '../../../js/integrations/telegram.js';
import { sendAnalysisToDiscord } from '../../../js/integrations/discord.js';

export class IntegrationManager extends BaseManager {
    constructor(controller) {
        super(controller);
    }

    async init() {
        await super.init();
    }

    /**
     * Send analysis to Telegram manually
     */
    async sendToTelegram(analysisId) {
        try {
            const analysisItem = await this.controller.dataManager.getAnalysisById(analysisId);
            if (!analysisItem) {
                this.controller.uiManager.showError('Analysis not found');
                return;
            }

            const analysisData = {
                analysisType: analysisItem.analysisType,
                result: analysisItem.result,
                date: new Date(analysisItem.date)
            };

            // This would typically get settings and send
            // For now, showing a placeholder implementation
            this.controller.uiManager.showSuccess('Telegram send functionality would be implemented here');
            
        } catch (error) {
            console.error('Error sending to Telegram:', error);
            this.controller.uiManager.showError('Failed to send to Telegram');
        }
    }

    /**
     * Send analysis to Discord manually
     */
    async sendToDiscord(analysisId) {
        try {
            const analysisItem = await this.controller.dataManager.getAnalysisById(analysisId);
            if (!analysisItem) {
                this.controller.uiManager.showError('Analysis not found');
                return;
            }

            const analysisData = {
                analysisType: analysisItem.analysisType,
                result: analysisItem.result,
                date: new Date(analysisItem.date)
            };

            // This would typically get settings and send
            // For now, showing a placeholder implementation
            this.controller.uiManager.showSuccess('Discord send functionality would be implemented here');
            
        } catch (error) {
            console.error('Error sending to Discord:', error);
            this.controller.uiManager.showError('Failed to send to Discord');
        }
    }
}
