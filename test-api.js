// Simple test script for the Vercel API
// Run with: node test-api.js

const API_URL = 'https://hustleplug-pro-validation-mswm0dc9o-calel33s-projects.vercel.app/api/validate-key';

async function testAPI() {
    console.log('🧪 Testing Vercel Pro Validation API\n');
    
    const testCases = [
        {
            name: 'Valid Demo Key',
            key: 'pro_demo_key_12345',
            expected: 'Should be valid'
        },
        {
            name: 'Invalid Key',
            key: 'invalid_key_123',
            expected: 'Should be invalid'
        },
        {
            name: 'Expired Demo Key',
            key: 'expired_demo_key_11111',
            expected: 'Should be expired'
        }
    ];
    
    for (const testCase of testCases) {
        console.log(`\n📋 Testing: ${testCase.name}`);
        console.log(`🔑 Key: ${testCase.key}`);
        console.log(`📝 Expected: ${testCase.expected}`);
        console.log('⏳ Sending request...');
        
        try {
            const response = await fetch(API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ key: testCase.key })
            });
            
            if (!response.ok) {
                console.log(`❌ HTTP Error: ${response.status} ${response.statusText}`);
                continue;
            }
            
            const result = await response.json();
            
            console.log('✅ Response received:');
            console.log(`   Success: ${result.success}`);
            console.log(`   Is Pro: ${result.isPro}`);
            console.log(`   Message: ${result.message}`);
            
            if (result.membershipDetails) {
                console.log(`   Status: ${result.membershipDetails.status}`);
                console.log(`   Tier: ${result.membershipDetails.tier}`);
                console.log(`   Days Remaining: ${result.membershipDetails.daysRemaining}`);
            }
            
        } catch (error) {
            console.log(`❌ Error: ${error.message}`);
        }
        
        console.log('─'.repeat(50));
    }
    
    console.log('\n🎯 API Test Complete!');
}

// Run the test
testAPI().catch(console.error); 