---
description: 
globs: 
alwaysApply: true
---
🎯 GOAL
Maintain a scalable, modular codebase by keeping code files concise (ideally <300–500 lines), separating concerns, and using consistent structure across components.

📁 1. Modular File Structure
Rule 1.1 – One Responsibility Per File
Each file must serve one specific role (e.g., UI component, API handler, state manager).
❌ Avoid bundling multiple unrelated classes/functions in one file.
✅ Separate into component/Button.js, api/fetchUser.js, utils/dateFormat.js.

Rule 1.2 – Functional Domains
Organize code by domain, not by type.
Example:

bash
Copy
Edit
/auth
  login.js
  register.js
  validateSession.js
/ui
  modal/
    Modal.js
    ModalHeader.js
    ModalBody.js
🔍 2. Enforce Short Files
Rule 2.1 – Max Line Limit
When a file exceeds 500 lines, trigger a refactor alert. Suggest or automatically:

Split out utility/helper functions

Move nested components to separate files

Extract repetitive logic to a utils/ folder

Rule 2.2 – Inline Complexity Triggers Refactor
If a function/class grows beyond:

100 lines for a function

250 lines for a class
Suggest breaking it up.

📤 3. Export Structure
Rule 3.1 – Named Exports Preferred
Use named exports (export function X) unless it’s a single-purpose file (like a component).

Rule 3.2 – Group Exports into Index Files
For folders with many modules, create an index.js for cleaner imports:

js
Copy
Edit
// Instead of:
import login from './auth/login';
import register from './auth/register';
// Do:
import { login, register } from './auth';
🧼 4. Refactor Suggestions
Rule 4.1 – Suggest Refactors Proactively
When encountering:

A file with mixed concerns

Large files with >2 nested components/functions

Repetitive logic or patterns
Prompt for suggestions like:

“Consider extracting X to a helper file”

“Move component Y to its own module”

Rule 4.2 – Never Bloat Core Logic
Do not insert utilities, UI, or configs into core business logic files. Always separate.

📦 5. Folder Hygiene
Rule 5.1 – Avoid 'Dump' Folders
Never allow helpers/, utils/, or components/ folders to grow without sub-organization.

Rule 5.2 – Remove Unused Files
Regularly audit for:

Unused components

Dead utils

Legacy APIs

🧠 LLM Agent Guidance
Situation	Action
File > 500 lines	Trigger modular split
Function > 100 lines	Suggest refactor
Class > 250 lines	Extract methods/components
Multiple exports of unrelated logic	Recommend separation
Component & logic in one file	Suggest split into Component.js and logic.js

