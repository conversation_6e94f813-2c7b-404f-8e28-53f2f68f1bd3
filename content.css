/* Agent Hu<PERSON>le Pro Analyzer - Content Styles */

/* Analysis Panel Styles */
#agent-hustle-analysis-panel {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
    line-height: 1.5 !important;
    color: #333 !important;
    box-sizing: border-box !important;
}

#agent-hustle-analysis-panel * {
    box-sizing: border-box !important;
}

#agent-hustle-analysis-panel .analysis-content {
    font-size: 14px !important;
    line-height: 1.6 !important;
}

#agent-hustle-analysis-panel .analysis-content p {
    margin: 0 0 12px 0 !important;
    padding: 0 !important;
}

#agent-hustle-analysis-panel .analysis-content strong {
    font-weight: 600 !important;
    color: #333 !important;
}

#agent-hustle-analysis-panel .analysis-content em {
    font-style: italic !important;
    color: #555 !important;
}

#agent-hustle-analysis-panel .analysis-content code {
    background: #f8f9fa !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', <PERSON><PERSON><PERSON>, 'Courier New', monospace !important;
    font-size: 13px !important;
    color: #e83e8c !important;
}

#agent-hustle-analysis-panel .analysis-content h3,
#agent-hustle-analysis-panel .analysis-content h4,
#agent-hustle-analysis-panel .analysis-content h5 {
    margin: 16px 0 8px 0 !important;
    font-weight: 600 !important;
    color: #333 !important;
}

#agent-hustle-analysis-panel .analysis-content h3 {
    font-size: 16px !important;
}

#agent-hustle-analysis-panel .analysis-content h4 {
    font-size: 15px !important;
}

#agent-hustle-analysis-panel .analysis-content h5 {
    font-size: 14px !important;
}

#agent-hustle-analysis-panel .analysis-content ul,
#agent-hustle-analysis-panel .analysis-content ol {
    margin: 8px 0 12px 20px !important;
    padding: 0 !important;
}

#agent-hustle-analysis-panel .analysis-content li {
    margin: 4px 0 !important;
    padding: 0 !important;
}

/* Tool Calls Section */
#agent-hustle-analysis-panel .tool-calls-section {
    margin-top: 16px !important;
    padding-top: 16px !important;
    border-top: 1px solid #e9ecef !important;
}

#agent-hustle-analysis-panel .tool-calls-section h5 {
    margin: 0 0 12px 0 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #667eea !important;
}

#agent-hustle-analysis-panel .tool-call {
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px !important;
    margin-bottom: 8px !important;
}

#agent-hustle-analysis-panel .tool-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 8px !important;
}

#agent-hustle-analysis-panel .tool-name {
    font-weight: 600 !important;
    font-size: 13px !important;
    color: #495057 !important;
}

#agent-hustle-analysis-panel .tool-status.success {
    color: #28a745 !important;
}

#agent-hustle-analysis-panel .tool-status.error {
    color: #dc3545 !important;
}

#agent-hustle-analysis-panel .tool-content {
    font-size: 13px !important;
    color: #6c757d !important;
    background: white !important;
    padding: 8px !important;
    border-radius: 4px !important;
    border: 1px solid #dee2e6 !important;
}

/* Selection Tooltip Styles */
#agent-hustle-selection-tooltip {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    pointer-events: auto !important;
    z-index: 10000 !important;
}

#agent-hustle-selection-tooltip:hover {
    transform: scale(1.05) !important;
}

/* Notification Styles */
.agent-hustle-notification {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    pointer-events: none !important;
    z-index: 10001 !important;
}

/* Floating Action Button Styles */
#agent-hustle-fab {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    pointer-events: auto !important;
}

#agent-hustle-fab:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4) !important;
}

#agent-hustle-fab:active {
    transform: scale(0.95) !important;
}

/* Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes slideOutUp {
    from {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    to {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.95);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    #agent-hustle-analysis-panel {
        width: calc(100vw - 40px) !important;
        max-width: 350px !important;
        right: 20px !important;
        left: auto !important;
    }
    
    #agent-hustle-fab {
        bottom: 20px !important;
        right: 20px !important;
        width: 50px !important;
        height: 50px !important;
        font-size: 20px !important;
    }
    
    #agent-hustle-selection-tooltip {
        font-size: 11px !important;
        padding: 6px 10px !important;
    }
}

@media (max-width: 480px) {
    #agent-hustle-analysis-panel {
        width: calc(100vw - 20px) !important;
        right: 10px !important;
        top: 10px !important;
        max-height: calc(100vh - 20px) !important;
    }
    
    .agent-hustle-notification {
        max-width: calc(100vw - 40px) !important;
        font-size: 13px !important;
        padding: 10px 16px !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    #agent-hustle-analysis-panel {
        background: #1a1a1a !important;
        border-color: #333 !important;
        color: #e0e0e0 !important;
    }
    
    #agent-hustle-analysis-panel .analysis-content {
        color: #e0e0e0 !important;
    }
    
    #agent-hustle-analysis-panel .analysis-content strong {
        color: #fff !important;
    }
    
    #agent-hustle-analysis-panel .analysis-content code {
        background: #2d2d2d !important;
        color: #ff6b9d !important;
    }
    
    #agent-hustle-analysis-panel .tool-call {
        background: #2d2d2d !important;
        border-color: #444 !important;
    }
    
    #agent-hustle-analysis-panel .tool-content {
        background: #1a1a1a !important;
        border-color: #444 !important;
        color: #ccc !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    #agent-hustle-analysis-panel {
        border: 2px solid #000 !important;
    }
    
    #agent-hustle-selection-tooltip {
        border: 2px solid #fff !important;
    }
    
    .agent-hustle-notification {
        border: 2px solid #fff !important;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    #agent-hustle-analysis-panel,
    #agent-hustle-selection-tooltip,
    .agent-hustle-notification,
    #agent-hustle-fab {
        animation: none !important;
        transition: none !important;
    }
    
    #agent-hustle-fab:hover {
        transform: none !important;
    }
}

/* Print Styles */
@media print {
    #agent-hustle-analysis-panel,
    #agent-hustle-selection-tooltip,
    .agent-hustle-notification,
    #agent-hustle-fab {
        display: none !important;
    }
}

/* Focus Styles for Accessibility */
#agent-hustle-analysis-panel button:focus,
#agent-hustle-selection-tooltip:focus,
#agent-hustle-fab:focus {
    outline: 2px solid #667eea !important;
    outline-offset: 2px !important;
}

/* Ensure proper stacking context */
#agent-hustle-analysis-panel,
#agent-hustle-selection-tooltip,
.agent-hustle-notification,
#agent-hustle-fab {
    position: fixed !important;
    z-index: 2147483647 !important; /* Maximum z-index value */
} 