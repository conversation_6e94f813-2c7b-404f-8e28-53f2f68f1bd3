/**
 * Prompt UI Manager
 * Handles prompt-related UI operations and management
 */
import { BaseManager } from '../core/BaseManager.js';
import { promptManager } from '../../../js/user/promptManager.js';
import { checkProStatus } from '../../../js/user/proStatus.js';

export class PromptUIManager extends BaseManager {
    constructor(controller) {
        super(controller);
        this.currentEditingPrompt = null;
    }

    async init() {
        await super.init();
        // Load saved prompts select on initialization
        await this.loadSavedPromptsSelect();
    }

    /**
     * Load saved prompts for the select dropdown
     */
    async loadSavedPromptsSelect() {
        try {
            const prompts = await promptManager.getPrompts();
            const select = document.getElementById('savedPromptsSelect');
            
            if (!select) return;

            // Clear existing options
            select.innerHTML = '<option value="">Select a saved prompt...</option>';

            // Add prompts to select
            prompts.forEach(prompt => {
                const option = document.createElement('option');
                option.value = prompt.id;
                option.textContent = prompt.title;
                select.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading saved prompts:', error);
        }
    }

    /**
     * Load selected prompt into custom analysis form
     */
    async loadSelectedPrompt() {
        const select = document.getElementById('savedPromptsSelect');
        const promptId = select?.value;
        
        if (!promptId) {
            this.controller.uiManager.showError('Please select a prompt to load');
            return;
        }

        try {
            const prompts = await promptManager.getPrompts();
            const prompt = prompts.find(p => p.id === promptId);
            
            if (prompt) {
                const customPromptTextarea = document.getElementById('customPrompt');
                if (customPromptTextarea) {
                    customPromptTextarea.value = prompt.content;
                }
                await promptManager.incrementUsage(promptId);
                this.controller.uiManager.showSuccess(`Loaded: ${prompt.title}`);
            } else {
                this.controller.uiManager.showError('Prompt not found');
            }
        } catch (error) {
            console.error('Error loading selected prompt:', error);
            this.controller.uiManager.showError('Failed to load prompt');
        }
    }

    /**
     * Save current prompt from custom analysis form
     */
    async saveCurrentPrompt() {
        const customPromptTextarea = document.getElementById('customPrompt');
        const promptContent = customPromptTextarea?.value.trim();
        
        if (!promptContent) {
            this.controller.uiManager.showError('Please enter a prompt to save');
            return;
        }

        try {
            // Check Pro status
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                this.controller.uiManager.showError('Prompt management is a Pro feature. Please upgrade to continue.');
                this.controller.uiManager.showSection('upgradeSection');
                return;
            }

            // Generate a title from the first line or first 50 characters
            const title = promptContent.split('\n')[0].substring(0, 50) + (promptContent.length > 50 ? '...' : '');
            
            const promptData = {
                title: title,
                content: promptContent,
                tags: ['custom'],
                category: 'user-created'
            };

            await promptManager.savePrompt(promptData);
            await this.loadSavedPromptsSelect(); // Refresh the dropdown
            this.controller.uiManager.showSuccess('Prompt saved successfully!');
        } catch (error) {
            console.error('Error saving prompt:', error);
            this.controller.uiManager.showError('Failed to save prompt');
        }
    }

    /**
     * Load prompt management interface
     */
    async loadPromptManagement() {
        try {
            await this.refreshPromptList();
            await this.loadTagFilters();
        } catch (error) {
            console.error('Error loading prompt management:', error);
            this.controller.uiManager.showError('Failed to load prompt management');
        }
    }

    /**
     * Refresh the prompt list display
     */
    async refreshPromptList() {
        try {
            const prompts = await promptManager.getPrompts();
            
            // Apply current filter if any
            let filteredPrompts = prompts;
            if (this.controller.currentFilterTag) {
                filteredPrompts = prompts.filter(prompt => 
                    prompt.tags.includes(this.controller.currentFilterTag)
                );
            }

            // Get paginated data
            const paginatedData = this.controller.promptPagination.getPaginatedData(filteredPrompts);
            
            this.displayPromptList(paginatedData.data, paginatedData.pagination);
        } catch (error) {
            console.error('Error refreshing prompt list:', error);
            this.controller.uiManager.showError('Failed to refresh prompt list');
        }
    }

    /**
     * Display prompt list with pagination
     */
    displayPromptList(prompts, pagination = null) {
        const listContainer = document.getElementById('promptList');
        const paginationContainer = document.getElementById('promptPagination');
        
        if (!listContainer) return;

        if (prompts.length === 0) {
            listContainer.innerHTML = '<p style="text-align: center; color: #888;">No prompts found.</p>';
            if (paginationContainer) {
                paginationContainer.innerHTML = '';
            }
            return;
        }

        listContainer.innerHTML = prompts.map(prompt => `
            <div class="prompt-card" data-prompt-id="${prompt.id}">
                <div class="prompt-header">
                    <h4 class="prompt-title">${this.escapeHtml(prompt.title)}</h4>
                    <div class="prompt-actions">
                        <button class="btn-icon" data-action="use" data-prompt-id="${prompt.id}" title="Use Prompt">📝</button>
                        <button class="btn-icon" data-action="copy" data-prompt-id="${prompt.id}" title="Copy Content">📋</button>
                        <button class="btn-icon" data-action="edit" data-prompt-id="${prompt.id}" title="Edit">✏️</button>
                        <button class="btn-icon" data-action="delete" data-prompt-id="${prompt.id}" title="Delete">🗑️</button>
                    </div>
                </div>
                <div class="prompt-content">
                    ${this.escapeHtml(prompt.content.substring(0, 150))}${prompt.content.length > 150 ? '...' : ''}
                </div>
                <div class="prompt-footer">
                    <div class="prompt-tags">
                        ${prompt.tags.map(tag => `<span class="tag">${this.escapeHtml(tag)}</span>`).join('')}
                    </div>
                    <div class="prompt-date">
                        Updated: ${new Date(prompt.updatedAt).toLocaleDateString()}
                    </div>
                </div>
            </div>
        `).join('');

        // Update pagination controls if pagination data is provided
        if (pagination && paginationContainer) {
            paginationContainer.innerHTML = this.controller.promptPagination.generatePaginationHTML(pagination);
        }
    }

    /**
     * Load tag filters
     */
    async loadTagFilters() {
        try {
            const prompts = await promptManager.getPrompts();
            const allTags = [...new Set(prompts.flatMap(prompt => prompt.tags))];
            
            const filterContainer = document.getElementById('tagFilters');
            if (!filterContainer) return;

            filterContainer.innerHTML = `
                <button class="tag-filter ${!this.controller.currentFilterTag ? 'active' : ''}" data-tag="">All</button>
                ${allTags.map(tag => `
                    <button class="tag-filter ${this.controller.currentFilterTag === tag ? 'active' : ''}" data-tag="${this.escapeHtml(tag)}">${this.escapeHtml(tag)}</button>
                `).join('')}
            `;
        } catch (error) {
            console.error('Error loading tag filters:', error);
        }
    }

    /**
     * Search prompts
     */
    async searchPrompts(query) {
        try {
            const prompts = await promptManager.searchPrompts(query);
            this.displayPromptList(prompts);
        } catch (error) {
            console.error('Error searching prompts:', error);
            this.controller.uiManager.showError('Failed to search prompts');
        }
    }

    /**
     * Sort prompts
     */
    async sortPrompts(sortBy) {
        try {
            await this.refreshPromptList(); // This will apply the current sort
        } catch (error) {
            console.error('Error sorting prompts:', error);
            this.controller.uiManager.showError('Failed to sort prompts');
        }
    }

    /**
     * Open prompt editor
     */
    async openPromptEditor(prompt = null) {
        try {
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                this.controller.uiManager.showError('Prompt management is a Pro feature. Please upgrade to continue.');
                this.controller.uiManager.showSection('upgradeSection');
                return;
            }

            this.currentEditingPrompt = prompt;
            
            // Set modal content
            const titleInput = document.getElementById('promptTitle');
            const contentTextarea = document.getElementById('promptContent');
            const tagsInput = document.getElementById('promptTags');
            
            if (prompt) {
                if (titleInput) titleInput.value = prompt.title;
                if (contentTextarea) contentTextarea.value = prompt.content;
                if (tagsInput) tagsInput.value = prompt.tags.join(', ');
            } else {
                if (titleInput) titleInput.value = '';
                if (contentTextarea) contentTextarea.value = '';
                if (tagsInput) tagsInput.value = '';
            }

            // Show modal
            const modal = document.getElementById('promptEditorModal');
            if (modal) {
                modal.style.display = 'block';
            }
        } catch (error) {
            console.error('Error opening prompt editor:', error);
            this.controller.uiManager.showError('Failed to open prompt editor');
        }
    }

    /**
     * Save prompt edit
     */
    async savePromptEdit() {
        const titleInput = document.getElementById('promptTitle');
        const contentTextarea = document.getElementById('promptContent');
        const tagsInput = document.getElementById('promptTags');
        
        const title = titleInput?.value.trim();
        const content = contentTextarea?.value.trim();
        const tagsString = tagsInput?.value.trim();
        
        if (!title || !content) {
            this.controller.uiManager.showError('Please fill in title and content');
            return;
        }

        try {
            const tags = tagsString ? tagsString.split(',').map(tag => tag.trim()).filter(tag => tag) : [];
            
            const promptData = {
                title,
                content,
                tags,
                category: 'user-created'
            };

            if (this.currentEditingPrompt) {
                promptData.id = this.currentEditingPrompt.id;
            }

            await promptManager.savePrompt(promptData);
            this.controller.uiManager.closePromptEditor();
            await this.refreshPromptList();
            await this.loadSavedPromptsSelect();
            await this.loadTagFilters();
            this.controller.uiManager.showSuccess(this.currentEditingPrompt ? 'Prompt updated successfully' : 'Prompt created successfully');
        } catch (error) {
            console.error('Error saving prompt:', error);
            this.controller.uiManager.showError('Failed to save prompt');
        }
    }

    /**
     * Export prompts
     */
    async exportPrompts() {
        try {
            const prompts = await promptManager.getPrompts();
            const exportData = {
                exportDate: new Date().toISOString(),
                totalPrompts: prompts.length,
                prompts: prompts
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agent-hustle-prompts-${Date.now()}.json`;
            
            document.body.appendChild(a);
            a.click();
            
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.controller.uiManager.showSuccess('Prompts exported successfully!');
        } catch (error) {
            console.error('Error exporting prompts:', error);
            this.controller.uiManager.showError('Failed to export prompts');
        }
    }

    /**
     * Import prompts
     */
    async importPrompts(file) {
        if (!file) return;

        try {
            const text = await file.text();
            const data = JSON.parse(text);
            
            if (!data.prompts || !Array.isArray(data.prompts)) {
                throw new Error('Invalid prompt file format');
            }

            for (const prompt of data.prompts) {
                await promptManager.savePrompt(prompt);
            }

            await this.refreshPromptList();
            await this.loadSavedPromptsSelect();
            this.controller.uiManager.showSuccess(`Imported ${data.prompts.length} prompts successfully!`);
        } catch (error) {
            console.error('Error importing prompts:', error);
            this.controller.uiManager.showError('Failed to import prompts');
        }
    }

    /**
     * Utility method to escape HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}
