# Chrome Extension specific
*.crx
*.pem
key.pem
*.zip

# Node modules and package files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Environment variables and secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config.json
secrets.json

# API Keys and sensitive data
api-keys.txt
*.key
*.secret

# Build and distribution
dist/
build/
.tmp/
temp/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Cache directories
.cache/
.parcel-cache/

# Testing
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*~

# Development tools
.eslintcache
.stylelintcache

# Chrome extension development
manifest.json.backup
popup.html.backup
*.backup 

# YoYo AI version control directory
.yoyo/
