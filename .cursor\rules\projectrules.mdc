---
description: 
globs: 
alwaysApply: true
---
# Hu<PERSON><PERSON>Plug Chrome Extension - Project Rules

## 🎯 Project Overview
HustlePlug is a Chrome extension for automating social media interactions with pro membership validation, rate limiting, and database-backed user management. The project consists of a Chrome extension frontend and a Node.js API backend deployed on Render with Turso database.

## 📁 Architecture & File Organization

### Core Structure
```
hustleplug/
├── js/                     # Extension JavaScript modules
│   ├── auth/              # Authentication & validation
│   ├── integrations/      # Platform integrations (Telegram, Discord)
│   ├── security/          # Security utilities
│   ├── user/              # User management
│   └── utils/             # Shared utilities
├── vercel-api/            # Backend API (deployed on Render)
│   ├── api/               # API endpoints
│   ├── db/                # Database queries & connection
│   └── test-*.js          # API tests
├── styles/                # CSS stylesheets
├── admin/                 # Admin tools
└── icons/                 # Extension icons
```

## 🧱 Code Structure & Modularity Rules
- make sure you maintain existing functionality and UI not unnecessary code changes only complete task at hand and make sure your solution is backwards compatible

### File Size Limits
- **Maximum 500 lines per file** - If exceeded, refactor into smaller modules
- **Functions: 100 lines max** - Break into smaller functions
- **Classes: 250 lines max** - Extract methods or split responsibilities

### Modular Organization
- **One responsibility per file** - Each file serves a specific role
- **Domain-based organization** - Group by feature, not by type
- **Named exports preferred** - Use `export function X` unless single-purpose file
- **Index files for clean imports** - Group related exports

### Example Good Structure:
```javascript
// ✅ Good - Single responsibility
// js/auth/proValidator.js
export async function validateProKey(key) { ... }
export async function cacheProStatus(hash, status) { ... }

// ✅ Good - Domain grouping
// js/integrations/telegram/
//   ├── telegramAPI.js
//   ├── messageFormatter.js
//   └── index.js
```

## 🔧 API Development Rules

### Database Operations
- **Always use parameterized queries** - Prevent SQL injection
- **Use Turso batch operations** for atomic transactions
- **Implement fallback mechanisms** for database failures
- **Log errors but don't expose sensitive data**

### Rate Limiting
- **3 requests per 10-second window per IP**
- **Use Map-based in-memory tracking**
- **Return 429 status with retryAfter**
- **Clean up old entries periodically**

### Error Handling
```javascript
// ✅ Good - Graceful degradation
try {
    await logKeyUsage(keyId, ip, userAgent);
} catch (loggingError) {
    console.warn('⚠️ Usage logging failed, continuing:', loggingError.message);
    // Continue with main operation
}
```

### API Response Format
```javascript
// ✅ Standard success response
{
    success: true,
    isPro: boolean,
    message: string,
    membershipDetails: {
        status: 'active' | 'expired' | 'suspended',
        tier: string,
        usageCount: number,
        lastUsed: string (ISO),
        createdAt: string (ISO),
        expiresAt: string (ISO),
        daysRemaining: number,
        isExpired: boolean,
        notes: string
    }
}

// ✅ Standard error response
{
    success: false,
    message: string,
    rateLimited?: boolean,
    retryAfter?: number
}
```

## 🔐 Security Rules

### Key Management
- **Always hash keys with salt** - Use consistent salt across extension/API
- **Store only hashed keys** - Never store plain text keys
- **Use crypto.createHash('sha256')** for hashing
- **Validate input before processing**

### CORS & Headers
```javascript
// ✅ Required CORS headers
res.setHeader('Access-Control-Allow-Origin', '*');
res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Accept');
```

### Environment Variables
- **Required for production**: `TURSO_DATABASE_URL`, `TURSO_AUTH_TOKEN`
- **Validate env vars on startup** - Exit if missing critical vars
- **Use dotenv for local development**

## 🧪 Testing Rules

### Test File Naming
- `test-*.js` for API tests
- `test-*-debug.js` for debugging specific issues
- `test-*-comprehensive.js` for full test suites

### Test Categories
1. **Unit Tests** - Individual functions
2. **Integration Tests** - API endpoints
3. **Rate Limiting Tests** - Verify throttling
4. **Database Tests** - Connection and queries
5. **Browser Simulation** - Extension behavior

### Test Structure
```javascript
// ✅ Good test structure
async function testFeatureName() {
    console.log('🧪 Testing Feature Name\n');
    
    try {
        // Setup
        const testData = setupTestData();
        
        // Execute
        const result = await functionUnderTest(testData);
        
        // Verify
        console.log(`✅ Expected behavior: ${result.success}`);
        
        // Cleanup if needed
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}
```

## 📊 Logging & Monitoring

### Log Levels & Format
```javascript
// ✅ Consistent logging format
console.log('🔍 Validation request from IP: ${clientIP}');     // Info
console.warn('⚠️ Usage logging failed, continuing...');        // Warning  
console.error('❌ Validation error:', error);                  // Error
console.log('✅ Valid key used: ${notes}...');                // Success
console.log('🚫 Rate limit exceeded for IP: ${ip}');          // Rate limit
console.log('📊 Usage logged: Key ID ${id}');                 // Analytics
```

### Error Tracking
- **Log errors with context** - Include relevant data
- **Don't log sensitive information** - Truncate keys/tokens
- **Use structured logging** - Consistent format
- **Monitor rate limiting effectiveness**

## 🚀 Deployment Rules

### Git Workflow
- **Commit related changes together** - Logical groupings
- **Test locally before deploying** - Run test suite
- **Deploy to Render via GitHub** - Automatic deployment
- **Verify deployment success** - Check API health

### Environment Consistency
- **Match local and production configs** - Same database schema
- **Use same Node.js version** - Consistent runtime
- **Test with production-like data** - Realistic scenarios

## 🔄 Chrome Extension Rules

### Manifest & Permissions
- **Minimal permissions** - Only request what's needed
- **Content Security Policy** - Secure script execution
- **Background script efficiency** - Minimize resource usage

### User Experience
- **Graceful error handling** - Don't crash on API failures
- **Loading states** - Show progress indicators
- **Offline capability** - Cache validation results
- **Rate limit awareness** - Respect API limits

### Extension Architecture
```javascript
// ✅ Good extension structure
// popup.js - UI logic only
// proValidator.js - Validation logic
// proStatus.js - Status management
// config.js - Configuration constants
```

## 🛠️ Development Workflow

### Before Making Changes
1. **Understand the impact** - Check dependencies
2. **Read existing code** - Follow established patterns
3. **Test locally first** - Verify functionality
4. **Consider backwards compatibility** - Don't break existing features

### Code Review Checklist
- [ ] Follows file size limits
- [ ] Uses consistent error handling
- [ ] Includes proper logging
- [ ] Has appropriate tests
- [ ] Follows security best practices
- [ ] Maintains API response format
- [ ] Handles edge cases gracefully

### Refactoring Triggers
- File exceeds 500 lines
- Function exceeds 100 lines
- Repeated code patterns
- Mixed concerns in single file
- Complex nested logic

## 📝 Documentation Rules

### Code Comments
```javascript
// ✅ Good documentation
/**
 * Validate a pro key and return membership details
 * @param {string} plainKey - Plain text key to validate
 * @returns {Promise<Object>} - Validation result with membership details
 */
async function validateKey(plainKey) {
    // Hash the key using consistent salt
    const hashedKey = hashKey(plainKey);
    // ... implementation
}
```

### API Documentation
- **Document all endpoints** - Include request/response examples
- **Explain rate limiting** - Limits and headers
- **Provide error codes** - All possible responses
- **Include authentication** - How to use API keys

## 🎯 Performance Rules

### Database Optimization
- **Use indexes** - On frequently queried columns
- **Batch operations** - For multiple related queries
- **Connection pooling** - Efficient resource usage
- **Query optimization** - Avoid N+1 problems

### Extension Performance
- **Debounce API calls** - Prevent excessive requests
- **Cache responses** - Reduce API load
- **Lazy loading** - Load features when needed
- **Memory management** - Clean up event listeners

## 🔍 Debugging Rules

### Debug Information
- **Include request IDs** - For tracing
- **Log timing information** - Performance insights
- **Capture error context** - Relevant state
- **Use debug flags** - Conditional logging

### Production Debugging
- **Safe error messages** - Don't expose internals
- **Structured logs** - Easy to parse
- **Health check endpoints** - System status
- **Monitoring dashboards** - Key metrics

---

## 🚨 Critical Rules Summary

1. **Never exceed 500 lines per file** - Refactor immediately
2. **Always use parameterized queries** - Security first
3. **Implement graceful error handling** - Don't crash on failures
4. **Test rate limiting thoroughly** - Verify protection works
5. **Hash all keys before storage** - Security requirement
6. **Deploy via GitHub to Render** - Consistent process
7. **Log errors with context** - Debugging support
8. **Follow API response format** - Consistency
9. **Validate environment variables** - Fail fast on missing config
10. **Test locally before deploying** - Quality assurance

This rules file should be followed by all contributors to maintain code quality, security, and consistency across the HustlePlug project. 