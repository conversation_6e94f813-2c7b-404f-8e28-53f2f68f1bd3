// Test Chrome Extension Integration with New API
// This simulates how the extension validates pro keys
// Run with: node test-extension-integration.js

import { validateProKey } from './js/auth/proValidator.js';

async function testExtensionIntegration() {
    console.log('🔌 Testing Chrome Extension Integration with New API\n');
    console.log('=' * 60);
    
    const testCases = [
        {
            name: 'Valid Pro Key',
            key: 'pro_demo_key_12345',
            expectedIsPro: true
        },
        {
            name: 'Valid Premium Key', 
            key: 'premium_demo_key_67890',
            expectedIsPro: true
        },
        {
            name: 'Expired Key',
            key: 'expired_demo_key_11111',
            expectedIsPro: false
        },
        {
            name: 'Invalid Key',
            key: 'invalid_key_123',
            expectedIsPro: false
        },
        {
            name: 'Empty Key',
            key: '',
            expectedIsPro: false
        }
    ];
    
    console.log('🧪 Testing Extension Pro Validation System');
    console.log('-' * 50);
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    for (const testCase of testCases) {
        console.log(`\n📋 Testing: ${testCase.name}`);
        console.log(`🔑 Key: "${testCase.key}"`);
        console.log(`🎯 Expected isPro: ${testCase.expectedIsPro}`);
        console.log('⏳ Validating...');
        
        try {
            const result = await validateProKey(testCase.key);
            
            console.log('📊 Result:');
            console.log(`   isPro: ${result.isPro}`);
            console.log(`   cached: ${result.cached}`);
            console.log(`   message: ${result.message}`);
            
            if (result.membershipDetails) {
                console.log(`   📋 Membership Details:`);
                console.log(`      status: ${result.membershipDetails.status}`);
                console.log(`      tier: ${result.membershipDetails.tier}`);
                console.log(`      daysRemaining: ${result.membershipDetails.daysRemaining}`);
                console.log(`      lastUsed: ${result.membershipDetails.lastUsed}`);
            }
            
            // Validate result
            if (result.isPro === testCase.expectedIsPro) {
                console.log('✅ Test PASSED');
                passedTests++;
            } else {
                console.log(`❌ Test FAILED - Expected isPro: ${testCase.expectedIsPro}, Got: ${result.isPro}`);
            }
            
        } catch (error) {
            console.log(`❌ Test ERROR: ${error.message}`);
        }
        
        console.log('─' * 50);
        
        // Add small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Summary
    console.log('\n🎯 INTEGRATION TEST SUMMARY');
    console.log('=' * 50);
    
    console.log(`\n📊 Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 ALL TESTS PASSED!');
        console.log('✅ Chrome extension is properly integrated with the new API');
        console.log('✅ Pro validation system is working correctly');
        console.log('✅ Ready for production use');
    } else {
        console.log('⚠️  Some tests failed');
        console.log('❌ Check the extension configuration');
        console.log('❌ Verify API endpoint is correct');
    }
    
    console.log('\n📝 Next Steps:');
    console.log('1. Load the extension in Chrome for manual testing');
    console.log('2. Test pro features with valid keys');
    console.log('3. Verify fallback behavior with invalid keys');
    console.log('4. Check that cached validation works offline');
    
    console.log('\n🔧 Extension Loading Instructions:');
    console.log('1. Open Chrome and go to chrome://extensions/');
    console.log('2. Enable "Developer mode" (top right toggle)');
    console.log('3. Click "Load unpacked" and select this folder');
    console.log('4. The HustlePlug extension should appear in your extensions');
    console.log('5. Test pro key validation in the extension popup');
}

// Run the integration test
testExtensionIntegration().catch(console.error); 