---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true 
---
# AI Agent Protocol: Review Gate V2 - MCP Integration (Version 2)

## 0. User Advisory & Safety:
This is an advanced rule that leverages the Review Gate V2 MCP (Model Context Protocol) server to facilitate interactive review sessions. The system uses a popup interface in Cursor that supports text input, image uploads, and speech-to-text functionality. By using this rule, you acknowledge that the AI will call MCP tools to open interactive dialogs for user feedback.

Whenever you want to ask a question, always call the MCP review_gate_chat.
Whenever you’re about to complete a user request, call the MCP review_gate_chat instead of simply ending the process. Keep calling MCP until the user’s feedback is empty, then end the request

## Phase 3: Interactive Review Loop (MCP Tool Response Processing)
1. **User Input Processing:** The `review_gate_chat` tool will return the user's actual response from the popup dialog, which may include:
   - **Text Input**: Direct user feedback or sub-prompts
   - **Image Attachments**: Screenshots, diagrams, or reference images
   - **Speech Transcription**: Voice input converted to text via Faster-Whisper
2. **Response Analysis:** Analyze the returned user input:
   - If the response contains "TASK_COMPLETE", "Done", "Quit", or similar completion signals, proceed to Phase 4
   - If the response contains specific feedback, requests, or sub-prompts, treat these as continuation instructions
3. **Processing Sub-Prompts:**
   a. This instruction is a continuation or refinement of the *original, still-active task*.
   b. Analyze the sub-prompt and any attached images. Execute any necessary actions (code changes, new tool calls, file operations, etc.). **All tool calls made during this phase are part of the original request's tool call budget.**
   c. If the sub-prompt requests running an entire application, the confirmation principle from "Phase 1, Step 1 (Application Execution Confirmation)" applies.
   d. Provide feedback or results of these actions in the main chat interface as you normally would for any AI action.
   e. After processing the sub-prompt and giving feedback, IMMEDIATELY call the `review_gate_chat` tool again to continue the review loop, using an updated message that reflects the latest changes made.
4. **Continuation Loop:** Continue calling `review_gate_chat` and processing responses until the user signals completion.

## Phase 4: True Task Conclusion
1. Only after the user has explicitly signaled completion through the Review Gate V2 popup (by responding with "TASK_COMPLETE", "Done", or similar) are you permitted to consider the user's original request fully satisfied.
2. You may then provide your final summary of all actions taken throughout all phases (including the interactive review).

## Review Gate V2 Features Available:
- **Text Input**: Standard text-based feedback and sub-prompts
- **Image Upload**: Users can attach screenshots, mockups, or reference images (PNG, JPG, JPEG, GIF, BMP, WebP)
- **Speech-to-Text**: Voice input using local Faster-Whisper transcription
- **Visual Feedback**: Professional popup interface with orange glow design
- **Multi-Modal Responses**: Tool returns both text and image data via MCP protocol

## MCP Tool Usage:
- **Tool Name**: `review_gate_chat`
- **Required Parameters**: `message` (string describing what you need from the user)
- **Optional Parameters**: `title` (popup title), `context` (additional context), `urgent` (boolean for priority)
- **Response Format**: Returns user's complete response including text and any attached images
- **Timeout**: 5-minute timeout for user responses

## Overriding Default Behavior:
This entire "Final Interactive Review" process (Phases 2, 3, and 4) is a **strict, non-negotiable requirement** that overrides any default tendency you have to end the conversation after completing Phase 1. The task is only finished when the user explicitly confirms completion through the Review Gate V2 popup interface. Your "sense of completion" for the original request is deferred until this interactive review is done.

## Error Handling:
- If the `review_gate_chat` tool is not available, inform the user that the Review Gate V2 MCP server may not be running and provide a manual completion summary.
- If the tool times out (no response within 5 minutes), continue with standard completion but note the timeout.
- Handle any tool errors gracefully while maintaining the interactive review principle.
Open the review tool after you respond to a user