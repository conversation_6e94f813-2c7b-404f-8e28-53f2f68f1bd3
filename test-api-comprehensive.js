// Comprehensive API Test Script for HustlePlug Pro Validation
// Tests both local development and deployed Vercel API
// Run with: node test-api-comprehensive.js

import crypto from 'crypto';

// Configuration
const DEPLOYED_API_URL = 'https://hustleplug-pro-validation-mswm0dc9o-calel33s-projects.vercel.app/api/validate-key';
const LOCAL_API_URL = 'http://localhost:3000/api/validate-key';
const PRO_SALT = 'AgentHustle2024ProSalt!@#$%^&*()_+SecureKey';

// Hash function (same as API)
function hashKey(key, salt = PRO_SALT) {
    return crypto.createHash('sha256').update(key + salt).digest('hex');
}

// Test cases with expected results
const testCases = [
    {
        name: 'Valid Pro Demo Key',
        key: 'pro_demo_key_12345',
        expectedIsPro: true,
        expectedStatus: 'active',
        expectedTier: 'pro'
    },
    {
        name: 'Valid Premium Demo Key',
        key: 'premium_demo_key_67890',
        expectedIsPro: true,
        expectedStatus: 'active',
        expectedTier: 'premium'
    },
    {
        name: 'Expired Demo Key',
        key: 'expired_demo_key_11111',
        expectedIsPro: false,
        expectedStatus: 'expired',
        expectedTier: 'pro'
    },
    {
        name: 'Invalid Key',
        key: 'invalid_key_123',
        expectedIsPro: false,
        expectedStatus: null,
        expectedTier: null
    },
    {
        name: 'Empty Key',
        key: '',
        expectedIsPro: false,
        expectedStatus: null,
        expectedTier: null,
        expectError: true
    }
];

async function testEndpoint(apiUrl, testCase) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log(`🔑 Key: "${testCase.key}"`);
    console.log(`🔐 Hash: ${testCase.key ? hashKey(testCase.key) : 'N/A'}`);
    console.log(`🎯 Expected: isPro=${testCase.expectedIsPro}, status=${testCase.expectedStatus}`);
    console.log(`⏳ Sending request to: ${apiUrl}`);
    
    try {
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ key: testCase.key })
        });
        
        if (!response.ok) {
            console.log(`❌ HTTP Error: ${response.status} ${response.statusText}`);
            if (testCase.expectError) {
                console.log(`✅ Expected error occurred`);
                return { success: true, expectedError: true };
            }
            return { success: false, error: `HTTP ${response.status}` };
        }
        
        const result = await response.json();
        
        console.log('📊 Response received:');
        console.log(`   Success: ${result.success}`);
        console.log(`   Is Pro: ${result.isPro}`);
        console.log(`   Message: ${result.message}`);
        console.log(`   Legacy: ${result.legacy}`);
        console.log(`   Expired: ${result.expired}`);
        console.log(`   Has Key: ${result.hasKey}`);
        
        if (result.membershipDetails) {
            console.log(`   📋 Membership Details:`);
            console.log(`      Status: ${result.membershipDetails.status}`);
            console.log(`      Tier: ${result.membershipDetails.tier}`);
            console.log(`      Days Remaining: ${result.membershipDetails.daysRemaining}`);
            console.log(`      Usage Count: ${result.membershipDetails.usageCount}`);
            console.log(`      Last Used: ${result.membershipDetails.lastUsed}`);
            console.log(`      Notes: ${result.membershipDetails.notes}`);
        }
        
        // Validate results
        const validation = {
            success: true,
            checks: []
        };
        
        if (result.isPro !== testCase.expectedIsPro) {
            validation.success = false;
            validation.checks.push(`❌ isPro mismatch: expected ${testCase.expectedIsPro}, got ${result.isPro}`);
        } else {
            validation.checks.push(`✅ isPro correct: ${result.isPro}`);
        }
        
        if (testCase.expectedStatus && result.membershipDetails?.status !== testCase.expectedStatus) {
            validation.success = false;
            validation.checks.push(`❌ Status mismatch: expected ${testCase.expectedStatus}, got ${result.membershipDetails?.status}`);
        } else if (testCase.expectedStatus) {
            validation.checks.push(`✅ Status correct: ${result.membershipDetails.status}`);
        }
        
        if (testCase.expectedTier && result.membershipDetails?.tier !== testCase.expectedTier) {
            validation.success = false;
            validation.checks.push(`❌ Tier mismatch: expected ${testCase.expectedTier}, got ${result.membershipDetails?.tier}`);
        } else if (testCase.expectedTier) {
            validation.checks.push(`✅ Tier correct: ${result.membershipDetails.tier}`);
        }
        
        console.log('\n🔍 Validation Results:');
        validation.checks.forEach(check => console.log(`   ${check}`));
        
        return { success: validation.success, result, validation };
        
    } catch (error) {
        console.log(`❌ Network/Parse Error: ${error.message}`);
        if (testCase.expectError) {
            console.log(`✅ Expected error occurred`);
            return { success: true, expectedError: true };
        }
        return { success: false, error: error.message };
    }
}

async function testAPI() {
    console.log('🧪 Comprehensive HustlePlug Pro Validation API Test\n');
    console.log('=' * 60);
    
    // Test deployed API
    console.log('\n🚀 Testing DEPLOYED API');
    console.log('=' * 40);
    
    let deployedResults = [];
    for (const testCase of testCases) {
        const result = await testEndpoint(DEPLOYED_API_URL, testCase);
        deployedResults.push({ testCase: testCase.name, ...result });
        console.log('─' * 50);
        
        // Add delay between requests to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Summary
    console.log('\n📊 TEST SUMMARY');
    console.log('=' * 40);
    
    const deployedPassed = deployedResults.filter(r => r.success).length;
    const deployedTotal = deployedResults.length;
    
    console.log(`\n🚀 Deployed API Results: ${deployedPassed}/${deployedTotal} tests passed`);
    
    deployedResults.forEach(result => {
        const status = result.success ? '✅' : '❌';
        const error = result.error ? ` (${result.error})` : '';
        console.log(`   ${status} ${result.testCase}${error}`);
    });
    
    // Overall result
    console.log('\n🎯 OVERALL RESULT');
    console.log('=' * 40);
    
    if (deployedPassed === deployedTotal) {
        console.log('🎉 ALL TESTS PASSED! API is working correctly.');
    } else {
        console.log('⚠️  Some tests failed. Please check the API configuration.');
    }
    
    console.log('\n📝 Next Steps:');
    console.log('1. If tests pass, the API is ready for production use');
    console.log('2. Update your Chrome extension to use the deployed API URL');
    console.log('3. Add your real pro keys to the API using generate-hashes.js');
    console.log('4. Deploy updates to Vercel when adding new keys');
}

// Run the comprehensive test
testAPI().catch(console.error); 